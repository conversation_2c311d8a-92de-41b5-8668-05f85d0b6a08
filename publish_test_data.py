#!/usr/bin/env python3
"""
Test data publisher for MQTT to RabbitMQ Bridge
Simulates IoT devices sending data to MQTT broker
"""
import json
import time
import random
import argparse
from datetime import datetime, timezone
import paho.mqtt.client as mqtt

class IoTDataSimulator:
    """Simulates IoT devices publishing data to MQTT"""
    
    def __init__(self, broker_host='localhost', broker_port=1883):
        self.broker_host = broker_host
        self.broker_port = broker_port
        self.client = None
        self.device_types = {
            'tablet': self._generate_tablet_data,
            'sensor': self._generate_sensor_data,
            'gateway': self._generate_gateway_data
        }
    
    def connect(self):
        """Connect to MQTT broker"""
        self.client = mqtt.Client(client_id='iot_simulator')
        self.client.on_connect = self._on_connect
        self.client.on_disconnect = self._on_disconnect
        
        try:
            self.client.connect(self.broker_host, self.broker_port, 60)
            self.client.loop_start()
            time.sleep(1)  # Wait for connection
            return True
        except Exception as e:
            print(f"Failed to connect to MQTT broker: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from MQTT broker"""
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()
    
    def _on_connect(self, client, userdata, flags, rc):
        """MQTT connection callback"""
        if rc == 0:
            print(f"✓ Connected to MQTT broker at {self.broker_host}:{self.broker_port}")
        else:
            print(f"✗ Failed to connect to MQTT broker, return code {rc}")
    
    def _on_disconnect(self, client, userdata, rc):
        """MQTT disconnection callback"""
        print("Disconnected from MQTT broker")
    
    def _generate_tablet_data(self, device_id):
        """Generate tablet sensor data"""
        return {
            'device_id': device_id,
            'device_type': 'tablet',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'battery_level': random.randint(20, 100),
            'signal_strength': random.randint(-80, -30),
            'temperature': round(random.uniform(18.0, 35.0), 1),
            'humidity': round(random.uniform(30.0, 80.0), 1),
            'location': {
                'lat': round(random.uniform(40.0, 41.0), 6),
                'lon': round(random.uniform(-74.5, -73.5), 6)
            },
            'app_version': '1.2.3',
            'os_version': 'Android 12'
        }
    
    def _generate_sensor_data(self, device_id):
        """Generate environmental sensor data"""
        return {
            'device_id': device_id,
            'device_type': 'sensor',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'temperature': round(random.uniform(15.0, 40.0), 2),
            'humidity': round(random.uniform(20.0, 90.0), 2),
            'pressure': round(random.uniform(980.0, 1020.0), 1),
            'light_level': random.randint(0, 1000),
            'motion_detected': random.choice([True, False]),
            'air_quality': random.randint(50, 300),
            'noise_level': round(random.uniform(30.0, 80.0), 1)
        }
    
    def _generate_gateway_data(self, device_id):
        """Generate gateway status data"""
        return {
            'device_id': device_id,
            'device_type': 'gateway',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'uptime': random.randint(3600, 86400),
            'cpu_usage': round(random.uniform(10.0, 80.0), 1),
            'memory_usage': round(random.uniform(20.0, 70.0), 1),
            'network_status': random.choice(['connected', 'disconnected', 'limited']),
            'connected_devices': random.randint(5, 50),
            'data_throughput': round(random.uniform(1.0, 100.0), 2),
            'error_count': random.randint(0, 10)
        }
    
    def publish_single_message(self, device_type, device_id, topic_suffix='data'):
        """Publish a single message"""
        if device_type not in self.device_types:
            print(f"Unknown device type: {device_type}")
            return False
        
        # Generate data
        data = self.device_types[device_type](device_id)
        
        # Create topic
        topic = f"devices/{device_id}/{topic_suffix}"
        
        # Publish message
        message = json.dumps(data)
        result = self.client.publish(topic, message, qos=1)
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print(f"✓ Published: {topic} -> {device_type} data")
            return True
        else:
            print(f"✗ Failed to publish to {topic}")
            return False
    
    def simulate_continuous_data(self, device_configs, interval=5, duration=60):
        """Simulate continuous data from multiple devices"""
        print(f"Starting simulation for {duration} seconds with {interval}s intervals")
        print(f"Devices: {device_configs}")
        
        start_time = time.time()
        message_count = 0
        
        while time.time() - start_time < duration:
            for config in device_configs:
                device_type = config['type']
                device_id = config['id']
                
                if self.publish_single_message(device_type, device_id):
                    message_count += 1
            
            print(f"Published {len(device_configs)} messages (total: {message_count})")
            time.sleep(interval)
        
        print(f"Simulation completed. Total messages: {message_count}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='IoT Data Simulator for MQTT Bridge Testing')
    parser.add_argument('--host', default='localhost', help='MQTT broker host')
    parser.add_argument('--port', type=int, default=1883, help='MQTT broker port')
    parser.add_argument('--mode', choices=['single', 'continuous'], default='single',
                       help='Simulation mode')
    parser.add_argument('--device-type', choices=['tablet', 'sensor', 'gateway'],
                       default='tablet', help='Device type for single mode')
    parser.add_argument('--device-id', default='test_device_001', help='Device ID')
    parser.add_argument('--interval', type=int, default=5,
                       help='Interval between messages in continuous mode (seconds)')
    parser.add_argument('--duration', type=int, default=60,
                       help='Duration of continuous simulation (seconds)')
    
    args = parser.parse_args()
    
    # Create simulator
    simulator = IoTDataSimulator(args.host, args.port)
    
    # Connect to broker
    if not simulator.connect():
        return 1
    
    try:
        if args.mode == 'single':
            # Single message mode
            print(f"Publishing single {args.device_type} message from {args.device_id}")
            simulator.publish_single_message(args.device_type, args.device_id)
        
        elif args.mode == 'continuous':
            # Continuous simulation mode
            device_configs = [
                {'type': 'tablet', 'id': 'tablet_001'},
                {'type': 'tablet', 'id': 'tablet_002'},
                {'type': 'sensor', 'id': 'sensor_001'},
                {'type': 'sensor', 'id': 'sensor_002'},
                {'type': 'gateway', 'id': 'gateway_001'}
            ]
            
            simulator.simulate_continuous_data(
                device_configs,
                interval=args.interval,
                duration=args.duration
            )
    
    except KeyboardInterrupt:
        print("\nSimulation interrupted by user")
    
    finally:
        simulator.disconnect()
    
    return 0

if __name__ == "__main__":
    exit(main())

version: '3.8'

services:
  # MQTT Broker (<PERSON><PERSON><PERSON><PERSON>)
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: iot_mosquitto
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./mosquitto.conf:/mosquitto/config/mosquitto.conf
      - mosquitto_data:/mosquitto/data
      - mosquitto_logs:/mosquitto/log
    restart: unless-stopped
    networks:
      - iot_network
    healthcheck:
      test: ["CMD", "mosquitto_pub", "-h", "localhost", "-t", "test", "-m", "health_check"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: iot_rabbitmq
    ports:
      - "5672:5672"    # AMQP port
      - "15672:15672"  # Management UI
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped
    networks:
      - iot_network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (for future dashboard integration)
  redis:
    image: redis:7-alpine
    container_name: iot_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - iot_network
    command: redis-server --appendonly yes

  # MQTT to RabbitMQ Bridge
  bridge:
    build:
      context: ./bridge
      dockerfile: Dockerfile
    container_name: iot_bridge
    environment:
      # MQTT Configuration
      MQTT_BROKER_HOST: mosquitto
      MQTT_BROKER_PORT: 1883
      MQTT_CLIENT_ID: iot_bridge_docker
      MQTT_TOPIC_PATTERN: devices/#

      # RabbitMQ Configuration
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USERNAME: guest
      RABBITMQ_PASSWORD: guest
      RABBITMQ_QUEUE_NAME: iot_data

      # Logging Configuration
      LOG_LEVEL: INFO
      LOG_FORMAT: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    depends_on:
      mosquitto:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - iot_network
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

volumes:
  mosquitto_data:
  mosquitto_logs:
  rabbitmq_data:
  redis_data:

networks:
  iot_network:
    driver: bridge

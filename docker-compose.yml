services:
  # MQTT Broker (<PERSON><PERSON><PERSON><PERSON>)
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: iot_mosquitto
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./mosquitto.conf:/mosquitto/config/mosquitto.conf
      - mosquitto_data:/mosquitto/data
      - mosquitto_logs:/mosquitto/log
    restart: unless-stopped
    networks:
      - iot_network

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: iot_rabbitmq
    ports:
      - "5672:5672"    # AMQP port
      - "15672:15672"  # Management UI
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped
    networks:
      - iot_network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (for future dashboard integration)
  redis:
    image: redis:7-alpine
    container_name: iot_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - iot_network
    command: redis-server --appendonly yes

volumes:
  mosquitto_data:
  mosquitto_logs:
  rabbitmq_data:
  redis_data:

networks:
  iot_network:
    driver: bridge

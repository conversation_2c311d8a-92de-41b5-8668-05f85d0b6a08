 Prompt: MQTT to RabbitMQ Bridge for Real-Time IoT Pipeline
I’m building an IoT system to ingest data from tablets via MQTT and forward it to RabbitMQ for scalable processing.

I need a Python script that:

Connects to an MQTT broker (e.g., <PERSON>s<PERSON>tto) using paho-mqtt.

Subscribes to all device topics using a wildcard (e.g., devices/#).

Receives each message and publishes it into RabbitMQ using pika.

Wraps the forwarded message as a JSON with:

topic (from MQTT)

payload (decoded JSON content from MQTT)

Publishes into a durable RabbitMQ queue (e.g., iot_data).

Logs each step and handles exceptions.

Please structure the code into:

config.py for broker and queue settings,

bridge.py as the main script,

requirements.txt with dependencies (paho-mqtt, pika).

This bridge will serve as the ingestion layer for a real-time dashboard using Redis later. Make sure the RabbitMQ message is persistent and the MQTT client auto-reconnects.

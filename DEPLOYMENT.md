# Deployment Guide

This guide covers deployment options for the IoT Real-Time Pipeline.

## Prerequisites

- Docker and Docker Compose
- Git
- Make (optional, for convenience commands)

## Production Deployment

### 1. Docker Compose (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd IoT-Real-time

# Start all services
docker-compose up -d

# Verify services are running
docker-compose ps

# View logs
docker-compose logs -f bridge
```

### 2. Individual Service Deployment

#### MQTT Broker
```bash
docker run -d \
  --name iot-mosquitto \
  -p 1883:1883 -p 9001:9001 \
  -v $(pwd)/mosquitto.conf:/mosquitto/config/mosquitto.conf \
  eclipse-mosquitto:2.0
```

#### RabbitMQ
```bash
docker run -d \
  --name iot-rabbitmq \
  -p 5672:5672 -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=guest \
  -e RABBITMQ_DEFAULT_PASS=guest \
  rabbitmq:3.12-management
```

#### Redis
```bash
docker run -d \
  --name iot-redis \
  -p 6379:6379 \
  redis:7-alpine redis-server --appendonly yes
```

#### Bridge Application
```bash
# Build the bridge image
docker build -t iot-bridge ./bridge

# Run the bridge
docker run -d \
  --name iot-bridge \
  --link iot-mosquitto:mosquitto \
  --link iot-rabbitmq:rabbitmq \
  -e MQTT_BROKER_HOST=mosquitto \
  -e RABBITMQ_HOST=rabbitmq \
  iot-bridge
```

## Environment Configuration

### Production Environment Variables

Create a `.env` file or set environment variables:

```bash
# MQTT Configuration
MQTT_BROKER_HOST=your-mqtt-broker.com
MQTT_BROKER_PORT=1883
MQTT_USERNAME=your-mqtt-user
MQTT_PASSWORD=your-mqtt-password

# RabbitMQ Configuration
RABBITMQ_HOST=your-rabbitmq-server.com
RABBITMQ_USERNAME=your-rabbitmq-user
RABBITMQ_PASSWORD=your-rabbitmq-password

# Security
RABBITMQ_VHOST=/production
MQTT_CLIENT_ID=iot_bridge_prod

# Logging
LOG_LEVEL=WARNING
LOG_FILE=/var/log/iot-bridge.log
```

### Docker Compose Override

For production, create `docker-compose.override.yml`:

```yaml
version: '3.8'

services:
  bridge:
    environment:
      LOG_LEVEL: WARNING
      MQTT_BROKER_HOST: production-mqtt.example.com
      RABBITMQ_HOST: production-rabbitmq.example.com
    restart: always
    
  mosquitto:
    volumes:
      - ./production-mosquitto.conf:/mosquitto/config/mosquitto.conf
      
  rabbitmq:
    environment:
      RABBITMQ_DEFAULT_USER: production_user
      RABBITMQ_DEFAULT_PASS: secure_password
```

## Kubernetes Deployment

### Namespace
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: iot-pipeline
```

### ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: bridge-config
  namespace: iot-pipeline
data:
  MQTT_BROKER_HOST: "mosquitto-service"
  RABBITMQ_HOST: "rabbitmq-service"
  LOG_LEVEL: "INFO"
```

### Bridge Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: iot-bridge
  namespace: iot-pipeline
spec:
  replicas: 2
  selector:
    matchLabels:
      app: iot-bridge
  template:
    metadata:
      labels:
        app: iot-bridge
    spec:
      containers:
      - name: bridge
        image: iot-bridge:latest
        envFrom:
        - configMapRef:
            name: bridge-config
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
```

## Monitoring and Observability

### Health Checks

All services include health checks:
- **Bridge**: Python process health
- **MQTT**: Connection test
- **RabbitMQ**: Management API ping
- **Redis**: Redis ping

### Logging

Configure centralized logging:

```yaml
# docker-compose.yml addition
services:
  bridge:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### Metrics

For production monitoring, consider:
- **Prometheus** for metrics collection
- **Grafana** for visualization
- **AlertManager** for alerting

Example metrics to monitor:
- Message throughput
- Connection status
- Queue depth
- Error rates

## Security

### Network Security
```yaml
# docker-compose.yml
networks:
  iot_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### Secrets Management
```yaml
# Use Docker secrets for sensitive data
secrets:
  mqtt_password:
    external: true
  rabbitmq_password:
    external: true

services:
  bridge:
    secrets:
      - mqtt_password
      - rabbitmq_password
```

### TLS Configuration

For production, enable TLS:
- MQTT over TLS (port 8883)
- RabbitMQ with TLS
- Redis with AUTH

## Scaling

### Horizontal Scaling
```yaml
# Scale bridge instances
docker-compose up -d --scale bridge=3
```

### Load Balancing
- Use multiple bridge instances
- RabbitMQ clustering
- MQTT broker clustering

## Backup and Recovery

### Data Persistence
- RabbitMQ: Persistent volumes for queue data
- Redis: AOF persistence enabled
- Logs: Persistent volumes for log files

### Backup Strategy
```bash
# Backup RabbitMQ definitions
docker exec iot-rabbitmq rabbitmqctl export_definitions /tmp/definitions.json

# Backup Redis data
docker exec iot-redis redis-cli BGSAVE
```

## Troubleshooting

### Common Issues

1. **Bridge not connecting to MQTT**
   - Check MQTT broker accessibility
   - Verify credentials
   - Check firewall rules

2. **Messages not reaching RabbitMQ**
   - Verify RabbitMQ connection
   - Check queue declarations
   - Review bridge logs

3. **High memory usage**
   - Check message backlog
   - Verify consumer processing
   - Monitor queue depths

### Debug Commands
```bash
# View all logs
docker-compose logs

# Check service health
docker-compose ps

# Access bridge container
docker-compose exec bridge bash

# Monitor RabbitMQ
docker-compose exec rabbitmq rabbitmqctl list_queues
```

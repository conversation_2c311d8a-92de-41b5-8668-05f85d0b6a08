#!/usr/bin/env python3
"""
Example usage of separate MQTT and RabbitMQ clients
Demonstrates how to use the clients independently
"""
import time
import json
from datetime import datetime, timezone

from config import setup_logging
from mqtt_client import MQTT<PERSON>lient
from rabbitmq_client import RabbitMQ<PERSON>lient


def example_mqtt_publisher():
    """Example of using MQTT client as a publisher"""
    print("=== MQTT Publisher Example ===")
    
    # Setup logging
    logger = setup_logging()
    
    # Create MQTT client
    mqtt_client = MQTTClient()
    
    try:
        # Connect to broker
        if mqtt_client.connect():
            print("✓ Connected to MQTT broker")
            
            # Wait a moment for connection to establish
            time.sleep(1)
            
            # Publish some test messages
            test_messages = [
                {
                    'topic': 'devices/tablet001/data',
                    'payload': {
                        'temperature': 23.5,
                        'humidity': 65.2,
                        'timestamp': datetime.now(timezone.utc).isoformat()
                    }
                },
                {
                    'topic': 'devices/sensor001/status',
                    'payload': {
                        'battery': 85,
                        'signal_strength': -45,
                        'online': True
                    }
                }
            ]
            
            for msg in test_messages:
                if mqtt_client.publish(msg['topic'], msg['payload']):
                    print(f"✓ Published to {msg['topic']}")
                else:
                    print(f"✗ Failed to publish to {msg['topic']}")
                
                time.sleep(1)
        
        else:
            print("✗ Failed to connect to MQTT broker")
    
    finally:
        mqtt_client.disconnect()
        print("MQTT client disconnected")


def example_mqtt_subscriber():
    """Example of using MQTT client as a subscriber"""
    print("\n=== MQTT Subscriber Example ===")
    
    # Setup logging
    logger = setup_logging()
    
    # Message handler
    def on_message(topic, payload):
        print(f"📨 Received: {topic} -> {payload}")
    
    # Create MQTT client with message callback
    mqtt_client = MQTTClient(message_callback=on_message)
    
    try:
        # Connect to broker
        if mqtt_client.connect():
            print("✓ Connected to MQTT broker")
            print("Listening for messages for 10 seconds...")
            
            # Listen for messages
            time.sleep(10)
            
            print(f"Total messages received: {mqtt_client.get_message_count()}")
        
        else:
            print("✗ Failed to connect to MQTT broker")
    
    finally:
        mqtt_client.disconnect()
        print("MQTT client disconnected")


def example_rabbitmq_publisher():
    """Example of using RabbitMQ client as a publisher"""
    print("\n=== RabbitMQ Publisher Example ===")
    
    # Setup logging
    logger = setup_logging()
    
    # Create RabbitMQ client
    rabbitmq_client = RabbitMQClient()
    
    try:
        # Connect to broker
        if rabbitmq_client.connect():
            print("✓ Connected to RabbitMQ broker")
            
            # Publish some test messages
            test_messages = [
                {
                    'topic': 'devices/tablet001/data',
                    'payload': {
                        'temperature': 24.1,
                        'humidity': 62.8
                    },
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'bridge_id': 'example_publisher'
                },
                {
                    'topic': 'devices/gateway001/status',
                    'payload': {
                        'uptime': 3600,
                        'connected_devices': 15
                    },
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'bridge_id': 'example_publisher'
                }
            ]
            
            for msg in test_messages:
                if rabbitmq_client.publish(msg):
                    print(f"✓ Published message from {msg['topic']}")
                else:
                    print(f"✗ Failed to publish message from {msg['topic']}")
                
                time.sleep(1)
            
            print(f"Total messages published: {rabbitmq_client.get_message_count()}")
        
        else:
            print("✗ Failed to connect to RabbitMQ broker")
    
    finally:
        rabbitmq_client.disconnect()
        print("RabbitMQ client disconnected")


def example_rabbitmq_consumer():
    """Example of using RabbitMQ client as a consumer"""
    print("\n=== RabbitMQ Consumer Example ===")
    
    # Setup logging
    logger = setup_logging()
    
    # Message handler
    def on_message(ch, method, properties, body):
        try:
            message = json.loads(body.decode('utf-8'))
            print(f"📨 Consumed: {message['topic']} -> {message['payload']}")
            
            # Acknowledge the message
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            print(f"✗ Error processing message: {e}")
    
    # Create RabbitMQ client
    rabbitmq_client = RabbitMQClient()
    
    try:
        # Connect to broker
        if rabbitmq_client.connect():
            print("✓ Connected to RabbitMQ broker")
            
            # Get queue info
            queue_info = rabbitmq_client.get_queue_info()
            if queue_info:
                print(f"Queue info: {queue_info}")
            
            print("Starting to consume messages (press Ctrl+C to stop)...")
            
            # Start consuming (this will block)
            rabbitmq_client.consume(on_message)
        
        else:
            print("✗ Failed to connect to RabbitMQ broker")
    
    except KeyboardInterrupt:
        print("\nStopping consumer...")
        rabbitmq_client.stop_consuming()
    
    finally:
        rabbitmq_client.disconnect()
        print("RabbitMQ client disconnected")


def example_bridge_simulation():
    """Example simulating the bridge functionality with separate clients"""
    print("\n=== Bridge Simulation Example ===")
    
    # Setup logging
    logger = setup_logging()
    
    # Create clients
    mqtt_client = MQTTClient()
    rabbitmq_client = RabbitMQClient()
    
    # Message forwarding function
    def forward_message(topic, payload):
        forwarded_message = {
            'topic': topic,
            'payload': payload,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'bridge_id': 'example_bridge'
        }
        
        if rabbitmq_client.publish(forwarded_message):
            print(f"✓ Forwarded: {topic} -> RabbitMQ")
        else:
            print(f"✗ Failed to forward: {topic}")
    
    try:
        # Connect both clients
        if not mqtt_client.connect():
            print("✗ Failed to connect to MQTT broker")
            return
        
        if not rabbitmq_client.connect():
            print("✗ Failed to connect to RabbitMQ broker")
            return
        
        print("✓ Both clients connected")
        
        # Set message callback for MQTT
        mqtt_client.set_message_callback(forward_message)
        
        print("Bridge simulation running for 15 seconds...")
        print("Publish messages to 'devices/#' topics to see them forwarded")
        
        # Run for 15 seconds
        time.sleep(15)
        
        print(f"MQTT messages received: {mqtt_client.get_message_count()}")
        print(f"RabbitMQ messages published: {rabbitmq_client.get_message_count()}")
    
    finally:
        mqtt_client.disconnect()
        rabbitmq_client.disconnect()
        print("Both clients disconnected")


def main():
    """Main function to run examples"""
    import argparse
    
    parser = argparse.ArgumentParser(description='MQTT and RabbitMQ Client Examples')
    parser.add_argument('--example', choices=[
        'mqtt-pub', 'mqtt-sub', 'rabbitmq-pub', 'rabbitmq-sub', 'bridge-sim', 'all'
    ], default='all', help='Which example to run')
    
    args = parser.parse_args()
    
    if args.example == 'mqtt-pub' or args.example == 'all':
        example_mqtt_publisher()
    
    if args.example == 'mqtt-sub' or args.example == 'all':
        example_mqtt_subscriber()
    
    if args.example == 'rabbitmq-pub' or args.example == 'all':
        example_rabbitmq_publisher()
    
    if args.example == 'rabbitmq-sub':
        example_rabbitmq_consumer()
    
    if args.example == 'bridge-sim' or args.example == 'all':
        example_bridge_simulation()


if __name__ == "__main__":
    main()

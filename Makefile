# IoT Real-time Pipeline Makefile

.PHONY: help build up down logs test clean dev-setup

# Default target
help:
	@echo "IoT Real-time Pipeline Commands:"
	@echo ""
	@echo "Infrastructure:"
	@echo "  build     - Build all Docker images"
	@echo "  up        - Start all services"
	@echo "  down      - Stop all services"
	@echo "  logs      - View logs from all services"
	@echo "  logs-bridge - View bridge logs only"
	@echo ""
	@echo "Development:"
	@echo "  dev-setup - Set up local development environment"
	@echo "  test      - Run test suite"
	@echo "  clean     - Clean up containers and volumes"
	@echo ""
	@echo "Bridge:"
	@echo "  bridge-build - Build bridge image only"
	@echo "  bridge-run   - Run bridge locally"

# Infrastructure commands
build:
	docker compose build

up:
	docker compose up -d

down:
	docker compose down

logs:
	docker compose logs -f

logs-bridge:
	docker compose logs -f bridge

# Development commands
dev-setup:
	@echo "Setting up development environment..."
	cd bridge && pip install -r requirements.txt
	cd testing && pip install -r requirements.txt
	@echo "Development environment ready!"

test:
	@echo "Running test suite..."
	cd testing && python test_bridge.py

clean:
	docker compose down -v
	docker system prune -f

# Bridge specific commands
bridge-build:
	docker compose build bridge

bridge-run:
	cd bridge && python bridge.py

# Quick start
start: build up
	@echo "IoT Pipeline started! Services:"
	@echo "- MQTT Broker: localhost:1883"
	@echo "- RabbitMQ Management: http://localhost:15672"
	@echo "- RabbitMQ AMQP: localhost:5672"
	@echo "- Redis: localhost:6379"
	@echo ""
	@echo "View logs with: make logs"

# Status check
status:
	docker compose ps

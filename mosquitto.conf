# Mosquitto MQTT Broker Configuration for IoT Bridge

# Basic settings
persistence true
persistence_location /mosquitto/data/

# Logging
log_dest file /mosquitto/log/mosquitto.log
log_dest stdout
log_type error
log_type warning
log_type notice
log_type information

# Connection settings
connection_messages true
log_timestamp true

# Network settings
listener 1883
protocol mqtt

# WebSocket listener (optional, for web clients)
listener 9001
protocol websockets

# Security settings (allow anonymous for testing)
allow_anonymous true

# Uncomment and configure for authentication
# password_file /mosquitto/config/passwd
# acl_file /mosquitto/config/acl

# Message size limits
max_packet_size 100000
message_size_limit 100000

# Client settings
max_connections -1
max_keepalive 65535

# Persistence settings
autosave_interval 1800
autosave_on_changes false
persistent_client_expiration 2h

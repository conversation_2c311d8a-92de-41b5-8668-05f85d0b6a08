# MQTT to RabbitMQ Bridge Environment Configuration
# Copy this file to .env and modify as needed

# MQTT Broker Configuration
MQTT_BROKER_HOST=localhost
MQTT_BROKER_PORT=1883
MQTT_USERNAME=
MQTT_PASSWORD=
MQTT_CLIENT_ID=iot_bridge_client
MQTT_KEEPALIVE=60
MQTT_TOPIC_PATTERN=devices/#
MQTT_QOS=1
MQTT_CLEAN_SESSION=True
MQTT_RECONNECT_DELAY_MIN=1
MQTT_RECONNECT_DELAY_MAX=120

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/
RABBITMQ_QUEUE_NAME=iot_data
RABBITMQ_EXCHANGE_NAME=
RABBITMQ_ROUTING_KEY=iot_data
RABBITMQ_DURABLE=True
RABBITMQ_PERSISTENT=True
RABBITMQ_CONNECTION_TIMEOUT=30
RABBITMQ_HEARTBEAT=600

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=
LOG_MAX_BYTES=********
LOG_BACKUP_COUNT=5

# Bridge Configuration
BRIDGE_MAX_RETRIES=5
BRIDGE_RETRY_DELAY=5
BRIDGE_MESSAGE_TIMEOUT=30
BRIDGE_HEALTH_CHECK_INTERVAL=60

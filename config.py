"""
Configuration settings for MQTT to RabbitMQ Bridge
"""
import os
import logging

# MQTT Broker Configuration
MQTT_CONFIG = {
    'broker_host': os.getenv('MQTT_BROKER_HOST', 'localhost'),
    'broker_port': int(os.getenv('MQTT_BROKER_PORT', 1883)),
    'username': os.getenv('MQTT_USERNAME', None),
    'password': os.getenv('MQTT_PASSWORD', None),
    'client_id': os.getenv('MQTT_CLIENT_ID', 'iot_bridge_client'),
    'keepalive': int(os.getenv('MQTT_KEEPALIVE', 60)),
    'topic_pattern': os.getenv('MQTT_TOPIC_PATTERN', 'devices/#'),
    'qos': int(os.getenv('MQTT_QOS', 1)),
    'clean_session': os.getenv('MQTT_CLEAN_SESSION', 'True').lower() == 'true',
    'reconnect_delay_min': int(os.getenv('MQTT_RECONNECT_DELAY_MIN', 1)),
    'reconnect_delay_max': int(os.getenv('MQTT_RECONNECT_DELAY_MAX', 120))
}

# RabbitMQ Configuration
RABBITMQ_CONFIG = {
    'host': os.getenv('RABBITMQ_HOST', 'localhost'),
    'port': int(os.getenv('RABBITMQ_PORT', 5672)),
    'username': os.getenv('RABBITMQ_USERNAME', 'guest'),
    'password': os.getenv('RABBITMQ_PASSWORD', 'guest'),
    'virtual_host': os.getenv('RABBITMQ_VHOST', '/'),
    'queue_name': os.getenv('RABBITMQ_QUEUE_NAME', 'iot_data'),
    'exchange_name': os.getenv('RABBITMQ_EXCHANGE_NAME', ''),
    'routing_key': os.getenv('RABBITMQ_ROUTING_KEY', 'iot_data'),
    'durable': os.getenv('RABBITMQ_DURABLE', 'True').lower() == 'true',
    'persistent': os.getenv('RABBITMQ_PERSISTENT', 'True').lower() == 'true',
    'connection_timeout': int(os.getenv('RABBITMQ_CONNECTION_TIMEOUT', 30)),
    'heartbeat': int(os.getenv('RABBITMQ_HEARTBEAT', 600))
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': getattr(logging, os.getenv('LOG_LEVEL', 'INFO').upper()),
    'format': os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
    'file': os.getenv('LOG_FILE', None),  # If None, logs to console
    'max_bytes': int(os.getenv('LOG_MAX_BYTES', ********)),  # 10MB
    'backup_count': int(os.getenv('LOG_BACKUP_COUNT', 5))
}

# Bridge Configuration
BRIDGE_CONFIG = {
    'max_retries': int(os.getenv('BRIDGE_MAX_RETRIES', 5)),
    'retry_delay': int(os.getenv('BRIDGE_RETRY_DELAY', 5)),
    'message_timeout': int(os.getenv('BRIDGE_MESSAGE_TIMEOUT', 30)),
    'health_check_interval': int(os.getenv('BRIDGE_HEALTH_CHECK_INTERVAL', 60))
}

def setup_logging():
    """Setup logging configuration"""
    log_config = LOGGING_CONFIG.copy()

    # Create formatter
    formatter = logging.Formatter(log_config['format'])

    # Get root logger
    logger = logging.getLogger()
    logger.setLevel(log_config['level'])

    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Add console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # Add file handler if specified
    if log_config['file']:
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            log_config['file'],
            maxBytes=log_config['max_bytes'],
            backupCount=log_config['backup_count']
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger
"""
MQTT Client for IoT Bridge
Handles MQTT connection, subscription, and message reception
"""
import json
import logging
from typing import Callable, Optional, Dict, Any
from datetime import datetime, timezone

import paho.mqtt.client as mqtt

from config import MQTT_CONFIG


class MQTTClient:
    """
    MQTT client wrapper with auto-reconnection and message handling
    """
    
    def __init__(self, message_callback: Optional[Callable[[str, Any], None]] = None):
        """
        Initialize MQTT client
        
        Args:
            message_callback: Function to call when message is received (topic, payload)
        """
        self.logger = logging.getLogger(__name__)
        self.client = None
        self.message_callback = message_callback
        self.connected = False
        self.message_count = 0
        
        self.logger.info("MQTT Client initialized")
    
    def connect(self) -> bool:
        """
        Connect to MQTT broker
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Create MQTT client
            self.client = mqtt.Client(
                client_id=MQTT_CONFIG['client_id'],
                clean_session=MQTT_CONFIG['clean_session']
            )
            
            # Set callbacks
            self.client.on_connect = self._on_connect
            self.client.on_disconnect = self._on_disconnect
            self.client.on_message = self._on_message
            self.client.on_log = self._on_log
            
            # Set authentication if provided
            if MQTT_CONFIG['username'] and MQTT_CONFIG['password']:
                self.client.username_pw_set(
                    MQTT_CONFIG['username'],
                    MQTT_CONFIG['password']
                )
            
            # Enable auto-reconnect
            self.client.reconnect_delay_set(
                min_delay=MQTT_CONFIG['reconnect_delay_min'],
                max_delay=MQTT_CONFIG['reconnect_delay_max']
            )
            
            # Connect to broker
            self.client.connect(
                MQTT_CONFIG['broker_host'],
                MQTT_CONFIG['broker_port'],
                MQTT_CONFIG['keepalive']
            )
            
            # Start the network loop
            self.client.loop_start()
            
            self.logger.info("MQTT client connection initiated")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to MQTT broker: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from MQTT broker"""
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()
            self.connected = False
            self.logger.info("MQTT client disconnected")
    
    def subscribe(self, topic_pattern: Optional[str] = None, qos: Optional[int] = None) -> bool:
        """
        Subscribe to MQTT topic(s)
        
        Args:
            topic_pattern: Topic pattern to subscribe to (defaults to config)
            qos: QoS level (defaults to config)
            
        Returns:
            bool: True if subscription successful, False otherwise
        """
        if not self.client or not self.connected:
            self.logger.error("Cannot subscribe: MQTT client not connected")
            return False
        
        topic = topic_pattern or MQTT_CONFIG['topic_pattern']
        qos_level = qos if qos is not None else MQTT_CONFIG['qos']
        
        try:
            result, mid = self.client.subscribe(topic, qos=qos_level)
            if result == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"Subscribed to topic pattern: {topic} (QoS: {qos_level})")
                return True
            else:
                self.logger.error(f"Failed to subscribe to {topic}, result code: {result}")
                return False
        except Exception as e:
            self.logger.error(f"Exception during subscription: {e}")
            return False
    
    def publish(self, topic: str, payload: Any, qos: Optional[int] = None, retain: bool = False) -> bool:
        """
        Publish message to MQTT topic
        
        Args:
            topic: Topic to publish to
            payload: Message payload (will be JSON encoded if not string)
            qos: QoS level (defaults to config)
            retain: Retain flag
            
        Returns:
            bool: True if publish successful, False otherwise
        """
        if not self.client or not self.connected:
            self.logger.error("Cannot publish: MQTT client not connected")
            return False
        
        qos_level = qos if qos is not None else MQTT_CONFIG['qos']
        
        try:
            # Convert payload to string if needed
            if isinstance(payload, (dict, list)):
                message = json.dumps(payload)
            else:
                message = str(payload)
            
            result = self.client.publish(topic, message, qos=qos_level, retain=retain)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.debug(f"Published to {topic}: {message}")
                return True
            else:
                self.logger.error(f"Failed to publish to {topic}, result code: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"Exception during publish: {e}")
            return False
    
    def is_connected(self) -> bool:
        """Check if client is connected"""
        return self.connected and self.client and self.client.is_connected()
    
    def get_message_count(self) -> int:
        """Get total number of messages received"""
        return self.message_count
    
    def set_message_callback(self, callback: Callable[[str, Any], None]):
        """Set the message callback function"""
        self.message_callback = callback
    
    def _on_connect(self, client, userdata, flags, rc):
        """Callback for MQTT connection"""
        if rc == 0:
            self.connected = True
            self.logger.info(f"Connected to MQTT broker at {MQTT_CONFIG['broker_host']}:{MQTT_CONFIG['broker_port']}")
            
            # Auto-subscribe to configured topic pattern
            self.subscribe()
        else:
            self.connected = False
            self.logger.error(f"Failed to connect to MQTT broker, return code {rc}")
    
    def _on_disconnect(self, client, userdata, rc):
        """Callback for MQTT disconnection"""
        self.connected = False
        if rc != 0:
            self.logger.warning(f"Unexpected MQTT disconnection, return code {rc}")
        else:
            self.logger.info("MQTT client disconnected")
    
    def _on_message(self, client, userdata, msg):
        """Callback for received MQTT messages"""
        try:
            # Decode the message payload
            topic = msg.topic
            payload_str = msg.payload.decode('utf-8')
            
            # Try to parse as JSON, fallback to string if not valid JSON
            try:
                payload_data = json.loads(payload_str)
            except json.JSONDecodeError:
                payload_data = payload_str
                self.logger.debug(f"Message from {topic} is not valid JSON, treating as string")
            
            self.message_count += 1
            self.logger.debug(f"Received message from {topic}, total: {self.message_count}")
            
            # Call the message callback if set
            if self.message_callback:
                self.message_callback(topic, payload_data)
            
        except Exception as e:
            self.logger.error(f"Error processing MQTT message from {msg.topic}: {e}")
    
    def _on_log(self, client, userdata, level, buf):
        """Callback for MQTT client logs"""
        self.logger.debug(f"MQTT: {buf}")

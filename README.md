# IoT Real-Time Pipeline

A containerized MQTT to RabbitMQ bridge system for real-time IoT data processing.

## Overview

This project implements a scalable IoT data ingestion pipeline that:

- **Receives** IoT device data via MQTT (from tablets, sensors, gateways)
- **Processes** and enriches messages with metadata
- **Forwards** data to RabbitMQ for reliable, scalable processing
- **Supports** real-time dashboards and analytics (Redis integration ready)

## Architecture

```
┌─────────────┐    MQTT     ┌─────────────┐    AMQP     ┌─────────────┐
│ IoT Devices │ ──────────► │   Bridge    │ ──────────► │  RabbitMQ   │
│ (Tablets,   │  devices/#  │ Application │  iot_data   │   Queue     │
│  Sensors)   │             │             │             │             │
└─────────────┘             └─────────────┘             └─────────────┘
                                   │
                                   ▼
                            ┌─────────────┐
                            │    Redis    │
                            │ (Dashboard) │
                            └─────────────┘
```

### Components

- **Bridge Application** (`bridge/`) - Containerized Python application
- **MQTT Broker** - Eclipse Mosquitto for device communication
- **Message Queue** - RabbitMQ for reliable message processing
- **Cache/Dashboard** - Redis for real-time data access
- **Testing Suite** (`testing/`) - Comprehensive test scripts

## Quick Start

```bash
# Start all services (recommended)
make start

# Or manually with docker-compose
docker compose up -d

# Check status
make status

# View logs
make logs-bridge
```

## Project Structure

```
IoT-Real-time/
├── bridge/                 # Bridge application
│   ├── bridge.py          # Main orchestrator
│   ├── mqtt_client.py     # MQTT client class
│   ├── rabbitmq_client.py # RabbitMQ client class
│   ├── config.py          # Configuration
│   ├── Dockerfile         # Container definition
│   └── requirements.txt   # Dependencies
├── testing/               # Test scripts
│   ├── test_bridge.py     # Integration tests
│   ├── example_usage.py   # Usage examples
│   └── publish_test_data.py # Data simulator
├── docker-compose.yml     # Infrastructure setup
├── Makefile              # Common operations
└── SETUP.md              # Detailed setup guide
```

## Services

When you run `docker compose up`, the following services start:

### Infrastructure Services
- **mosquitto** - MQTT broker (ports 1883, 9001)
- **rabbitmq** - Message queue with management UI (ports 5672, 15672)
- **redis** - Cache and real-time data store (port 6379)

### Application Services
- **bridge** - MQTT to RabbitMQ bridge application

## Service URLs

- **MQTT Broker**: `localhost:1883` (MQTT), `localhost:9001` (WebSocket)
- **RabbitMQ Management**: http://localhost:15672 (guest/guest)
- **RabbitMQ AMQP**: `localhost:5672`
- **Redis**: `localhost:6379`

## Message Flow

1. **IoT devices** publish data to MQTT topics (`devices/#`)
2. **Bridge application** subscribes to MQTT topics
3. **Messages are processed** and wrapped with metadata:
   ```json
   {
     "topic": "devices/tablet001/data",
     "payload": {"temperature": 23.5, "humidity": 65.2},
     "timestamp": "2024-01-01T12:00:01.123456Z",
     "bridge_id": "iot_bridge_docker"
   }
   ```
4. **Processed messages** are published to RabbitMQ queue (`iot_data`)
5. **Downstream services** can consume from RabbitMQ for processing

## Development

### Local Development
```bash
# Set up development environment
make dev-setup

# Run bridge locally (infrastructure must be running)
cd bridge
python bridge.py
```

### Testing
```bash
# Run integration tests
make test

# Run specific examples
cd testing
python3 example_usage.py --example mqtt-pub
python3 publish_test_data.py --mode continuous
```

### Building and Deployment
```bash
# Build all images
make build

# Build bridge image only
make bridge-build

# Clean up
make clean
```

## Configuration

The bridge application is configured via environment variables in `docker-compose.yml`. For local development, copy `bridge/.env.example` to `bridge/.env`.

Key configuration options:
- **MQTT_BROKER_HOST** - MQTT broker hostname
- **RABBITMQ_HOST** - RabbitMQ hostname  
- **MQTT_TOPIC_PATTERN** - MQTT subscription pattern (default: `devices/#`)
- **RABBITMQ_QUEUE_NAME** - Target queue name (default: `iot_data`)
- **LOG_LEVEL** - Logging verbosity

## Monitoring

- **Health Checks**: All services include health checks
- **Logs**: View with `make logs` or `docker-compose logs -f`
- **RabbitMQ Management**: Monitor queues at http://localhost:15672
- **Bridge Statistics**: Logged periodically with message counts

## Next Steps

This pipeline serves as the foundation for:
- Real-time IoT dashboards
- Data analytics and processing
- Alert and notification systems
- Historical data storage
- Machine learning pipelines

For detailed setup instructions, see [SETUP.md](SETUP.md).

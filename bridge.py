"""
MQTT to RabbitMQ Bridge - Main Script
Connects to MQTT broker, subscribes to device topics, and forwards messages to RabbitMQ
"""
import signal
import sys
import threading
import time
from datetime import datetime, timezone
from typing import Dict, Any

from config import <PERSON><PERSON><PERSON>_CONFIG, BRIDGE_CONFIG, setup_logging
from mqtt_client import MQ<PERSON><PERSON><PERSON>
from rabbitmq_client import RabbitMQClient

class MQTTRabbitMQBridge:
    """
    Bridge class that connects MQTT and RabbitMQ using separate client classes
    Handles message forwarding with persistence and auto-reconnection
    """

    def __init__(self):
        self.logger = setup_logging()
        self.mqtt_client = None
        self.rabbitmq_client = None
        self.running = False
        self.message_count = 0
        self.error_count = 0

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        self.logger.info("MQTT to RabbitMQ Bridge initialized")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)

    def _setup_clients(self):
        """Setup MQTT and RabbitMQ clients"""
        try:
            # Initialize RabbitMQ client
            self.rabbitmq_client = RabbitMQClient()
            if not self.rabbitmq_client.connect():
                raise Exception("Failed to connect to RabbitMQ")

            # Initialize MQTT client with message callback
            self.mqtt_client = MQTTClient(message_callback=self._on_mqtt_message)
            if not self.mqtt_client.connect():
                raise Exception("Failed to connect to MQTT broker")

            self.logger.info("Both MQTT and RabbitMQ clients initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to setup clients: {e}")
            raise
    
    def _on_mqtt_message(self, topic: str, payload: Any):
        """Callback for received MQTT messages from MQTT client"""
        try:
            # Create the forwarded message structure
            forwarded_message = {
                'topic': topic,
                'payload': payload,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'bridge_id': MQTT_CONFIG['client_id']
            }

            # Forward to RabbitMQ
            if self._forward_to_rabbitmq(forwarded_message):
                self.message_count += 1
                self.logger.debug(f"Processed message from {topic}, total: {self.message_count}")
            else:
                self.error_count += 1

        except Exception as e:
            self.error_count += 1
            self.logger.error(f"Error processing MQTT message from {topic}: {e}")
    
    def _forward_to_rabbitmq(self, message: Dict[str, Any]) -> bool:
        """Forward message to RabbitMQ using the RabbitMQ client"""
        try:
            return self.rabbitmq_client.publish(message)
        except Exception as e:
            self.logger.error(f"Failed to forward message to RabbitMQ: {e}")
            return False
    
    def start(self):
        """Start the bridge service"""
        try:
            self.logger.info("Starting MQTT to RabbitMQ Bridge...")

            # Setup client connections
            self._setup_clients()

            self.running = True
            self.logger.info("Bridge started successfully")

            # Start health check thread
            health_thread = threading.Thread(target=self._health_check_loop, daemon=True)
            health_thread.start()

            # Keep the main thread alive
            while self.running:
                time.sleep(1)

        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt, shutting down...")
            self.stop()
        except Exception as e:
            self.logger.error(f"Failed to start bridge: {e}")
            self.stop()
            raise
    
    def stop(self):
        """Stop the bridge service gracefully"""
        self.logger.info("Stopping MQTT to RabbitMQ Bridge...")
        self.running = False

        # Disconnect MQTT client
        if self.mqtt_client:
            self.mqtt_client.disconnect()

        # Disconnect RabbitMQ client
        if self.rabbitmq_client:
            self.rabbitmq_client.disconnect()

        self.logger.info(f"Bridge stopped. Processed {self.message_count} messages with {self.error_count} errors")
    
    def _health_check_loop(self):
        """Periodic health check for connections"""
        while self.running:
            try:
                time.sleep(BRIDGE_CONFIG['health_check_interval'])

                if not self.running:
                    break

                # Check MQTT connection
                if not self.mqtt_client or not self.mqtt_client.is_connected():
                    self.logger.warning("MQTT client not connected")

                # Check RabbitMQ connection
                if not self.rabbitmq_client or not self.rabbitmq_client.is_connected():
                    self.logger.warning("RabbitMQ client not connected")

                # Log statistics
                mqtt_count = self.mqtt_client.get_message_count() if self.mqtt_client else 0
                rabbitmq_count = self.rabbitmq_client.get_message_count() if self.rabbitmq_client else 0

                self.logger.info(f"Health check: {self.message_count} messages processed, {self.error_count} errors")
                self.logger.info(f"MQTT received: {mqtt_count}, RabbitMQ published: {rabbitmq_count}")

            except Exception as e:
                self.logger.error(f"Health check error: {e}")

def main():
    """Main entry point"""
    try:
        bridge = MQTTRabbitMQBridge()
        bridge.start()
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

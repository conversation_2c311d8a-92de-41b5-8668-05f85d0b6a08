"""
MQTT to RabbitMQ Bridge - Main Script
Connects to MQTT broker, subscribes to device topics, and forwards messages to RabbitMQ
"""
import json
import time
import signal
import sys
import threading
from datetime import datetime, timezone
from typing import Optional, Dict, Any

import paho.mqtt.client as mqtt
import pika
from pika.exceptions import AMQPConnectionError, AMQPChannelError
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from config import MQTT_<PERSON>NFIG, RABBITMQ_CONFIG, BRIDGE_CONFIG, setup_logging

class MQTTRabbitMQBridge:
    """
    Bridge class that connects MQTT and RabbitMQ
    Handles message forwarding with persistence and auto-reconnection
    """
    
    def __init__(self):
        self.logger = setup_logging()
        self.mqtt_client = None
        self.rabbitmq_connection = None
        self.rabbitmq_channel = None
        self.running = False
        self.message_count = 0
        self.error_count = 0
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info("MQTT to RabbitMQ Bridge initialized")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)
    
    @retry(
        stop=stop_after_attempt(BRIDGE_CONFIG['max_retries']),
        wait=wait_exponential(multiplier=1, min=1, max=60),
        retry=retry_if_exception_type((AMQPConnectionError, AMQPChannelError))
    )
    def _setup_rabbitmq(self):
        """Setup RabbitMQ connection and channel with retry logic"""
        try:
            # Create connection parameters
            credentials = pika.PlainCredentials(
                RABBITMQ_CONFIG['username'],
                RABBITMQ_CONFIG['password']
            )
            
            parameters = pika.ConnectionParameters(
                host=RABBITMQ_CONFIG['host'],
                port=RABBITMQ_CONFIG['port'],
                virtual_host=RABBITMQ_CONFIG['virtual_host'],
                credentials=credentials,
                connection_attempts=3,
                retry_delay=2,
                socket_timeout=RABBITMQ_CONFIG['connection_timeout'],
                heartbeat=RABBITMQ_CONFIG['heartbeat']
            )
            
            # Establish connection
            self.rabbitmq_connection = pika.BlockingConnection(parameters)
            self.rabbitmq_channel = self.rabbitmq_connection.channel()
            
            # Declare queue as durable
            self.rabbitmq_channel.queue_declare(
                queue=RABBITMQ_CONFIG['queue_name'],
                durable=RABBITMQ_CONFIG['durable']
            )
            
            self.logger.info(f"Connected to RabbitMQ at {RABBITMQ_CONFIG['host']}:{RABBITMQ_CONFIG['port']}")
            self.logger.info(f"Queue '{RABBITMQ_CONFIG['queue_name']}' declared as durable")
            
        except Exception as e:
            self.logger.error(f"Failed to setup RabbitMQ: {e}")
            raise
    
    def _setup_mqtt(self):
        """Setup MQTT client with auto-reconnection"""
        try:
            self.mqtt_client = mqtt.Client(
                client_id=MQTT_CONFIG['client_id'],
                clean_session=MQTT_CONFIG['clean_session']
            )
            
            # Set callbacks
            self.mqtt_client.on_connect = self._on_mqtt_connect
            self.mqtt_client.on_disconnect = self._on_mqtt_disconnect
            self.mqtt_client.on_message = self._on_mqtt_message
            self.mqtt_client.on_log = self._on_mqtt_log
            
            # Set authentication if provided
            if MQTT_CONFIG['username'] and MQTT_CONFIG['password']:
                self.mqtt_client.username_pw_set(
                    MQTT_CONFIG['username'],
                    MQTT_CONFIG['password']
                )
            
            # Enable auto-reconnect
            self.mqtt_client.reconnect_delay_set(
                min_delay=MQTT_CONFIG['reconnect_delay_min'],
                max_delay=MQTT_CONFIG['reconnect_delay_max']
            )
            
            self.logger.info("MQTT client configured")
            
        except Exception as e:
            self.logger.error(f"Failed to setup MQTT client: {e}")
            raise
    
    def _on_mqtt_connect(self, client, userdata, flags, rc):
        """Callback for MQTT connection"""
        if rc == 0:
            self.logger.info(f"Connected to MQTT broker at {MQTT_CONFIG['broker_host']}:{MQTT_CONFIG['broker_port']}")
            # Subscribe to device topics
            client.subscribe(MQTT_CONFIG['topic_pattern'], qos=MQTT_CONFIG['qos'])
            self.logger.info(f"Subscribed to topic pattern: {MQTT_CONFIG['topic_pattern']}")
        else:
            self.logger.error(f"Failed to connect to MQTT broker, return code {rc}")
    
    def _on_mqtt_disconnect(self, client, userdata, rc):
        """Callback for MQTT disconnection"""
        if rc != 0:
            self.logger.warning(f"Unexpected MQTT disconnection, return code {rc}")
        else:
            self.logger.info("MQTT client disconnected")
    
    def _on_mqtt_message(self, client, userdata, msg):
        """Callback for received MQTT messages"""
        try:
            # Decode the message payload
            topic = msg.topic
            payload_str = msg.payload.decode('utf-8')
            
            # Try to parse as JSON, fallback to string if not valid JSON
            try:
                payload_data = json.loads(payload_str)
            except json.JSONDecodeError:
                payload_data = payload_str
                self.logger.warning(f"Message from {topic} is not valid JSON, treating as string")
            
            # Create the forwarded message structure
            forwarded_message = {
                'topic': topic,
                'payload': payload_data,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'bridge_id': MQTT_CONFIG['client_id']
            }
            
            # Forward to RabbitMQ
            self._forward_to_rabbitmq(forwarded_message)
            
            self.message_count += 1
            self.logger.debug(f"Processed message from {topic}, total: {self.message_count}")
            
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"Error processing MQTT message from {msg.topic}: {e}")
    
    def _on_mqtt_log(self, client, userdata, level, buf):
        """Callback for MQTT client logs"""
        self.logger.debug(f"MQTT: {buf}")
    
    @retry(
        stop=stop_after_attempt(BRIDGE_CONFIG['max_retries']),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((AMQPConnectionError, AMQPChannelError))
    )
    def _forward_to_rabbitmq(self, message: Dict[str, Any]):
        """Forward message to RabbitMQ with retry logic"""
        try:
            # Ensure RabbitMQ connection is alive
            if not self.rabbitmq_connection or self.rabbitmq_connection.is_closed:
                self.logger.warning("RabbitMQ connection lost, reconnecting...")
                self._setup_rabbitmq()
            
            # Convert message to JSON string
            message_body = json.dumps(message)
            
            # Publish message with persistence
            self.rabbitmq_channel.basic_publish(
                exchange=RABBITMQ_CONFIG['exchange_name'],
                routing_key=RABBITMQ_CONFIG['routing_key'],
                body=message_body,
                properties=pika.BasicProperties(
                    delivery_mode=2 if RABBITMQ_CONFIG['persistent'] else 1,  # 2 = persistent
                    content_type='application/json',
                    timestamp=int(time.time())
                )
            )
            
            self.logger.debug(f"Forwarded message to RabbitMQ queue '{RABBITMQ_CONFIG['queue_name']}'")
            
        except Exception as e:
            self.logger.error(f"Failed to forward message to RabbitMQ: {e}")
            raise
    
    def start(self):
        """Start the bridge service"""
        try:
            self.logger.info("Starting MQTT to RabbitMQ Bridge...")
            
            # Setup connections
            self._setup_rabbitmq()
            self._setup_mqtt()
            
            # Connect to MQTT broker
            self.mqtt_client.connect(
                MQTT_CONFIG['broker_host'],
                MQTT_CONFIG['broker_port'],
                MQTT_CONFIG['keepalive']
            )
            
            self.running = True
            self.logger.info("Bridge started successfully")
            
            # Start the MQTT loop in a separate thread
            self.mqtt_client.loop_start()
            
            # Start health check thread
            health_thread = threading.Thread(target=self._health_check_loop, daemon=True)
            health_thread.start()
            
            # Keep the main thread alive
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt, shutting down...")
            self.stop()
        except Exception as e:
            self.logger.error(f"Failed to start bridge: {e}")
            self.stop()
            raise
    
    def stop(self):
        """Stop the bridge service gracefully"""
        self.logger.info("Stopping MQTT to RabbitMQ Bridge...")
        self.running = False
        
        # Disconnect MQTT client
        if self.mqtt_client:
            self.mqtt_client.loop_stop()
            self.mqtt_client.disconnect()
            self.logger.info("MQTT client disconnected")
        
        # Close RabbitMQ connection
        if self.rabbitmq_channel and not self.rabbitmq_channel.is_closed:
            self.rabbitmq_channel.close()
            self.logger.info("RabbitMQ channel closed")
        
        if self.rabbitmq_connection and not self.rabbitmq_connection.is_closed:
            self.rabbitmq_connection.close()
            self.logger.info("RabbitMQ connection closed")
        
        self.logger.info(f"Bridge stopped. Processed {self.message_count} messages with {self.error_count} errors")
    
    def _health_check_loop(self):
        """Periodic health check for connections"""
        while self.running:
            try:
                time.sleep(BRIDGE_CONFIG['health_check_interval'])
                
                if not self.running:
                    break
                
                # Check MQTT connection
                if not self.mqtt_client.is_connected():
                    self.logger.warning("MQTT client not connected, attempting reconnection...")
                
                # Check RabbitMQ connection
                if not self.rabbitmq_connection or self.rabbitmq_connection.is_closed:
                    self.logger.warning("RabbitMQ connection lost, will reconnect on next message")
                
                # Log statistics
                self.logger.info(f"Health check: {self.message_count} messages processed, {self.error_count} errors")
                
            except Exception as e:
                self.logger.error(f"Health check error: {e}")

def main():
    """Main entry point"""
    try:
        bridge = MQTTRabbitMQBridge()
        bridge.start()
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

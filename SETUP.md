# MQTT to RabbitMQ Bridge Setup Guide

This guide will help you set up and run the MQTT to RabbitMQ bridge for your IoT real-time pipeline.

## Architecture

The bridge uses a modular architecture with separate client classes:

- **`MQTTClient`** (`mqtt_client.py`) - Handles MQTT connections, subscriptions, and message reception
- **`RabbitMQClient`** (`rabbitmq_client.py`) - Handles RabbitMQ connections, queue management, and message publishing
- **`MQTTRabbitMQBridge`** (`bridge.py`) - Orchestrates the clients and handles message forwarding

This separation allows for better testing, maintainability, and reusability of the individual components.

## Prerequisites

- Python 3.8 or higher
- Docker and Docker Compose (for infrastructure)
- Git (for version control)

## Quick Start

### 1. Install Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt
```

### 2. Start Infrastructure Services

```bash
# Start MQTT broker, RabbitMQ, and Redis using Docker Compose
docker-compose up -d

# Check if services are running
docker-compose ps
```

### 3. Configure Environment (Optional)

```bash
# Copy example environment file
cp .env.example .env

# Edit .env file with your specific configuration
nano .env
```

### 4. Run the Bridge

```bash
# Start the MQTT to RabbitMQ bridge
python bridge.py
```

### 5. Test the Bridge

```bash
# Run the test suite to verify functionality
python test_bridge.py
```

### 6. Example Usage of Separate Clients

```bash
# Run examples of using MQTT and RabbitMQ clients independently
python example_usage.py --example all

# Or run specific examples:
python example_usage.py --example mqtt-pub      # MQTT publisher
python example_usage.py --example mqtt-sub      # MQTT subscriber
python example_usage.py --example rabbitmq-pub  # RabbitMQ publisher
python example_usage.py --example rabbitmq-sub  # RabbitMQ consumer
python example_usage.py --example bridge-sim    # Bridge simulation
```

## Service URLs

Once the infrastructure is running:

- **MQTT Broker**: `localhost:1883` (MQTT), `localhost:9001` (WebSocket)
- **RabbitMQ Management**: http://localhost:15672 (guest/guest)
- **RabbitMQ AMQP**: `localhost:5672`
- **Redis**: `localhost:6379`

## Configuration

### MQTT Configuration

The bridge subscribes to the `devices/#` topic pattern by default. You can modify this in the configuration:

- **Topic Pattern**: `devices/#` (subscribes to all device topics)
- **QoS Level**: 1 (at least once delivery)
- **Auto-reconnect**: Enabled with exponential backoff

### RabbitMQ Configuration

Messages are published to a durable queue for persistence:

- **Queue Name**: `iot_data`
- **Durability**: Enabled (survives broker restarts)
- **Message Persistence**: Enabled (messages survive broker restarts)

### Message Format

The bridge wraps MQTT messages in the following JSON structure:

```json
{
  "topic": "devices/tablet001/data",
  "payload": {
    "temperature": 23.5,
    "humidity": 65.2,
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "timestamp": "2024-01-01T12:00:01.123456Z",
  "bridge_id": "iot_bridge_client"
}
```

## Testing

### Manual Testing

1. **Publish MQTT Message**:
   ```bash
   # Using mosquitto_pub (install mosquitto-clients)
   mosquitto_pub -h localhost -t "devices/tablet001/data" -m '{"temperature": 25.0, "humidity": 60.0}'
   ```

2. **Check RabbitMQ Queue**:
   - Open RabbitMQ Management UI: http://localhost:15672
   - Navigate to Queues → `iot_data`
   - View messages in the queue

3. **Consume RabbitMQ Messages**:
   ```bash
   # Using Python script
   python -c "
   import pika, json
   connection = pika.BlockingConnection(pika.ConnectionParameters('localhost'))
   channel = connection.channel()
   
   def callback(ch, method, properties, body):
       message = json.loads(body)
       print(f'Received: {message}')
       ch.basic_ack(delivery_tag=method.delivery_tag)
   
   channel.basic_consume(queue='iot_data', on_message_callback=callback)
   print('Waiting for messages. Press CTRL+C to exit')
   channel.start_consuming()
   "
   ```

### Automated Testing

Run the comprehensive test suite:

```bash
python test_bridge.py
```

The test suite verifies:
- Basic message forwarding
- Invalid JSON handling
- Message structure validation
- Connection resilience

## Monitoring

### Logs

The bridge provides comprehensive logging:

```bash
# View real-time logs
tail -f bridge.log

# Or if logging to console
python bridge.py | tee bridge.log
```

### Health Checks

The bridge includes built-in health monitoring:
- Connection status checks every 60 seconds
- Automatic reconnection on failures
- Message and error counters

### RabbitMQ Monitoring

Use the RabbitMQ Management UI to monitor:
- Queue depth and message rates
- Connection status
- Consumer activity

## Production Deployment

### Environment Variables

Set production configuration via environment variables:

```bash
export MQTT_BROKER_HOST=your-mqtt-broker.com
export RABBITMQ_HOST=your-rabbitmq-server.com
export LOG_LEVEL=WARNING
export LOG_FILE=/var/log/iot-bridge.log
```

### Systemd Service

Create a systemd service for automatic startup:

```ini
# /etc/systemd/system/iot-bridge.service
[Unit]
Description=MQTT to RabbitMQ Bridge
After=network.target

[Service]
Type=simple
User=iot-bridge
WorkingDirectory=/opt/iot-bridge
ExecStart=/usr/bin/python3 bridge.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### Docker Deployment

Build and run as a Docker container:

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "bridge.py"]
```

## Troubleshooting

### Common Issues

1. **Connection Refused**:
   - Ensure MQTT broker and RabbitMQ are running
   - Check firewall settings
   - Verify connection parameters

2. **Messages Not Forwarded**:
   - Check MQTT topic subscription pattern
   - Verify RabbitMQ queue exists
   - Review bridge logs for errors

3. **High Memory Usage**:
   - Check for message backlog in RabbitMQ
   - Verify consumers are processing messages
   - Consider message TTL settings

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
export LOG_LEVEL=DEBUG
python bridge.py
```

## Next Steps

This bridge serves as the ingestion layer for a real-time dashboard using Redis. Future enhancements may include:

- Redis integration for real-time data caching
- Web dashboard for monitoring
- Data transformation and filtering
- Multiple output destinations
- Metrics and alerting integration

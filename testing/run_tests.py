#!/usr/bin/env python3
"""
Test runner script that ensures infrastructure is running before tests
"""
import subprocess
import sys
import time
import os

def check_docker_compose():
    """Check if docker compose is available"""
    try:
        result = subprocess.run(['docker', 'compose', 'version'], 
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

def check_services_running():
    """Check if required services are running"""
    try:
        # Check if containers are running
        result = subprocess.run(['docker', 'compose', 'ps', '--services', '--filter', 'status=running'], 
                              capture_output=True, text=True, timeout=10, cwd='..')
        running_services = result.stdout.strip().split('\n') if result.stdout.strip() else []
        
        required_services = ['mosquitto', 'rabbitmq', 'redis']
        missing_services = [svc for svc in required_services if svc not in running_services]
        
        return len(missing_services) == 0, missing_services
    except Exception as e:
        print(f"Error checking services: {e}")
        return False, []

def start_infrastructure():
    """Start the infrastructure services"""
    print("Starting infrastructure services...")
    try:
        # Start infrastructure services (not the bridge)
        result = subprocess.run(['docker', 'compose', 'up', '-d', 'mosquitto', 'rabbitmq', 'redis'], 
                              cwd='..', timeout=60)
        if result.returncode == 0:
            print("✓ Infrastructure services started")
            # Wait for services to be ready
            print("Waiting for services to be ready...")
            time.sleep(10)
            return True
        else:
            print("✗ Failed to start infrastructure services")
            return False
    except subprocess.TimeoutExpired:
        print("✗ Timeout starting infrastructure services")
        return False
    except Exception as e:
        print(f"✗ Error starting infrastructure: {e}")
        return False

def start_bridge():
    """Start the bridge application"""
    print("Starting bridge application...")
    try:
        # Change to bridge directory and start bridge
        bridge_process = subprocess.Popen([sys.executable, 'bridge.py'], 
                                        cwd='../bridge',
                                        stdout=subprocess.PIPE,
                                        stderr=subprocess.PIPE)
        
        # Wait a bit for bridge to start
        time.sleep(5)
        
        # Check if bridge is still running
        if bridge_process.poll() is None:
            print("✓ Bridge application started")
            return bridge_process
        else:
            stdout, stderr = bridge_process.communicate()
            print(f"✗ Bridge failed to start:")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"✗ Error starting bridge: {e}")
        return None

def run_tests():
    """Run the test suite"""
    print("\nRunning test suite...")
    try:
        result = subprocess.run([sys.executable, 'test_bridge.py'], 
                              timeout=120)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("✗ Tests timed out")
        return False
    except Exception as e:
        print(f"✗ Error running tests: {e}")
        return False

def main():
    """Main test runner"""
    print("IoT Bridge Test Runner")
    print("=" * 30)
    
    # Check if docker compose is available
    if not check_docker_compose():
        print("✗ Docker Compose not available. Please install Docker and Docker Compose.")
        print("Trying to run tests with existing services...")
    else:
        # Check if services are running
        services_running, missing = check_services_running()
        
        if not services_running:
            print(f"Missing services: {missing}")
            if not start_infrastructure():
                print("✗ Failed to start infrastructure. Please start manually with:")
                print("  cd .. && docker compose up -d mosquitto rabbitmq redis")
                sys.exit(1)
        else:
            print("✓ Infrastructure services are already running")
    
    # Start bridge
    bridge_process = start_bridge()
    
    try:
        # Run tests
        success = run_tests()
        
        if success:
            print("\n🎉 All tests completed successfully!")
            sys.exit(0)
        else:
            print("\n⚠️  Some tests failed. Check the output above.")
            sys.exit(1)
            
    finally:
        # Clean up bridge process
        if bridge_process and bridge_process.poll() is None:
            print("\nStopping bridge application...")
            bridge_process.terminate()
            try:
                bridge_process.wait(timeout=10)
                print("✓ Bridge stopped")
            except subprocess.TimeoutExpired:
                bridge_process.kill()
                print("✓ Bridge force stopped")

if __name__ == "__main__":
    main()

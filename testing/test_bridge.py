"""
Test script for MQTT to RabbitMQ Bridge
Provides utilities to test the bridge functionality
"""
import json
import time
import threading
import sys
import os

# Add bridge directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'bridge'))

import paho.mqtt.client as mqtt
import pika
from datetime import datetime, timezone

# Test configuration
TEST_MQTT_CONFIG = {
    'broker_host': 'localhost',
    'broker_port': 1883,
    'client_id': 'test_publisher',
    'topic_base': 'devices'
}

TEST_RABBITMQ_CONFIG = {
    'host': 'localhost',
    'port': 5672,
    'username': 'guest',
    'password': 'guest',
    'queue_name': 'iot_data'
}

class BridgeTestSuite:
    """Test suite for the MQTT to RabbitMQ bridge"""

    def __init__(self):
        self.mqtt_client = None
        self.rabbitmq_connection = None
        self.rabbitmq_channel = None
        self.received_messages = []

    def wait_for_services(self, max_wait=30):
        """Wait for MQTT and RabbitMQ services to be available"""
        print("Waiting for services to be ready...")

        # Wait for MQTT
        mqtt_ready = False
        for i in range(max_wait):
            try:
                test_client = mqtt.Client(client_id='test_connection_check')
                test_client.connect(TEST_MQTT_CONFIG['broker_host'], TEST_MQTT_CONFIG['broker_port'], 5)
                test_client.disconnect()
                mqtt_ready = True
                print("✓ MQTT broker is ready")
                break
            except Exception:
                time.sleep(1)

        if not mqtt_ready:
            raise Exception("MQTT broker not available after waiting")

        # Wait for RabbitMQ
        rabbitmq_ready = False
        for i in range(max_wait):
            try:
                credentials = pika.PlainCredentials(
                    TEST_RABBITMQ_CONFIG['username'],
                    TEST_RABBITMQ_CONFIG['password']
                )
                parameters = pika.ConnectionParameters(
                    host=TEST_RABBITMQ_CONFIG['host'],
                    port=TEST_RABBITMQ_CONFIG['port'],
                    credentials=credentials,
                    socket_timeout=5
                )
                test_conn = pika.BlockingConnection(parameters)
                test_conn.close()
                rabbitmq_ready = True
                print("✓ RabbitMQ broker is ready")
                break
            except Exception:
                time.sleep(1)

        if not rabbitmq_ready:
            raise Exception("RabbitMQ broker not available after waiting")

        print("✓ All services are ready")
        
    def setup_mqtt_publisher(self):
        """Setup MQTT client for publishing test messages"""
        self.mqtt_client = mqtt.Client(client_id=TEST_MQTT_CONFIG['client_id'])
        self.mqtt_client.connect(
            TEST_MQTT_CONFIG['broker_host'],
            TEST_MQTT_CONFIG['broker_port'],
            60
        )
        self.mqtt_client.loop_start()
        print("✓ MQTT test publisher connected")
    
    def setup_rabbitmq_consumer(self):
        """Setup RabbitMQ consumer to verify message delivery"""
        try:
            # Close existing connection if any
            if self.rabbitmq_connection and not self.rabbitmq_connection.is_closed:
                self.rabbitmq_connection.close()

            credentials = pika.PlainCredentials(
                TEST_RABBITMQ_CONFIG['username'],
                TEST_RABBITMQ_CONFIG['password']
            )

            parameters = pika.ConnectionParameters(
                host=TEST_RABBITMQ_CONFIG['host'],
                port=TEST_RABBITMQ_CONFIG['port'],
                credentials=credentials,
                connection_attempts=3,
                retry_delay=1,
                socket_timeout=10,
                heartbeat=600
            )

            self.rabbitmq_connection = pika.BlockingConnection(parameters)
            self.rabbitmq_channel = self.rabbitmq_connection.channel()

            # Declare queue (should already exist from bridge)
            self.rabbitmq_channel.queue_declare(
                queue=TEST_RABBITMQ_CONFIG['queue_name'],
                durable=True
            )

            print("✓ RabbitMQ test consumer connected")

        except Exception as e:
            print(f"✗ Failed to setup RabbitMQ consumer: {e}")
            raise
    
    def consume_rabbitmq_messages(self, timeout=10):
        """Consume messages from RabbitMQ queue"""
        def callback(ch, method, properties, body):
            try:
                message = json.loads(body.decode('utf-8'))
                self.received_messages.append(message)
                print(f"✓ Received from RabbitMQ: {message['topic']} -> {message['payload']}")
                ch.basic_ack(delivery_tag=method.delivery_tag)
            except Exception as e:
                print(f"✗ Error processing RabbitMQ message: {e}")

        try:
            # Check if channel is still open
            if self.rabbitmq_channel.is_closed:
                print("RabbitMQ channel is closed, reconnecting...")
                self.setup_rabbitmq_consumer()

            self.rabbitmq_channel.basic_consume(
                queue=TEST_RABBITMQ_CONFIG['queue_name'],
                on_message_callback=callback
            )

            # Start consuming in a separate thread
            def consume_thread():
                try:
                    self.rabbitmq_channel.start_consuming()
                except Exception as e:
                    print(f"Consumer thread error: {e}")

            consumer_thread = threading.Thread(target=consume_thread, daemon=True)
            consumer_thread.start()

            # Wait for messages
            time.sleep(timeout)

            # Stop consuming safely
            try:
                if not self.rabbitmq_channel.is_closed:
                    self.rabbitmq_channel.stop_consuming()
            except Exception as e:
                print(f"Error stopping consumer: {e}")

        except Exception as e:
            print(f"Error in consume_rabbitmq_messages: {e}")
            # Try to reconnect
            try:
                self.setup_rabbitmq_consumer()
            except Exception as reconnect_error:
                print(f"Failed to reconnect: {reconnect_error}")
    
    def publish_test_message(self, device_id, payload, topic_suffix="data"):
        """Publish a test message to MQTT"""
        topic = f"{TEST_MQTT_CONFIG['topic_base']}/{device_id}/{topic_suffix}"
        message = json.dumps(payload)
        
        self.mqtt_client.publish(topic, message, qos=1)
        print(f"✓ Published to MQTT: {topic} -> {payload}")
        time.sleep(0.5)  # Small delay to ensure message is sent
    
    def test_basic_message_forwarding(self):
        """Test basic message forwarding from MQTT to RabbitMQ"""
        print("\n=== Testing Basic Message Forwarding ===")

        try:
            # Clear received messages
            self.received_messages = []

            # Test messages
            test_cases = [
                {
                    'device_id': 'tablet001',
                    'payload': {
                        'temperature': 23.5,
                        'humidity': 65.2,
                        'timestamp': datetime.now(timezone.utc).isoformat()
                    }
                }
            ]

            # Publish test messages
            for test_case in test_cases:
                self.publish_test_message(test_case['device_id'], test_case['payload'])

            # Wait a bit for the bridge to process
            time.sleep(2)

            # Start consuming and wait for messages
            print("Waiting for messages to be forwarded...")
            self.consume_rabbitmq_messages(timeout=8)

            # Verify results
            print(f"\nResults: {len(self.received_messages)} messages received")

            if len(self.received_messages) >= len(test_cases):
                print("✓ All test messages were successfully forwarded")
                return True
            else:
                print(f"✗ Expected {len(test_cases)} messages, got {len(self.received_messages)}")
                # Print received messages for debugging
                for i, msg in enumerate(self.received_messages):
                    print(f"  Message {i+1}: {msg}")
                return False

        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            return False
    
    def test_invalid_json_handling(self):
        """Test handling of non-JSON messages"""
        print("\n=== Testing Invalid JSON Handling ===")

        try:
            self.received_messages = []

            # Publish non-JSON message
            topic = f"{TEST_MQTT_CONFIG['topic_base']}/tablet003/raw"
            invalid_message = "This is not JSON data"

            self.mqtt_client.publish(topic, invalid_message, qos=1)
            print(f"✓ Published non-JSON message: {invalid_message}")

            # Wait for forwarding
            time.sleep(3)
            self.consume_rabbitmq_messages(timeout=5)

            # Check if message was still forwarded (as string)
            if self.received_messages:
                forwarded_msg = self.received_messages[0]
                if forwarded_msg['payload'] == invalid_message:
                    print("✓ Non-JSON message correctly forwarded as string")
                    return True
                else:
                    print(f"✗ Message payload mismatch. Expected: {invalid_message}, Got: {forwarded_msg['payload']}")
            else:
                print("✗ No messages received")

            return False

        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            return False
    
    def test_message_structure(self):
        """Test the structure of forwarded messages"""
        print("\n=== Testing Message Structure ===")

        try:
            self.received_messages = []

            # Publish a test message
            test_payload = {'test': 'structure_validation'}
            self.publish_test_message('test_device', test_payload)

            # Wait for processing
            time.sleep(3)

            # Consume message
            self.consume_rabbitmq_messages(timeout=5)

            if not self.received_messages:
                print("✗ No message received for structure test")
                return False

            message = self.received_messages[0]
            required_fields = ['topic', 'payload', 'timestamp', 'bridge_id']

            missing_fields = [field for field in required_fields if field not in message]

            if not missing_fields:
                print("✓ Message structure is correct")
                print(f"  - Topic: {message['topic']}")
                print(f"  - Payload: {message['payload']}")
                print(f"  - Timestamp: {message['timestamp']}")
                print(f"  - Bridge ID: {message['bridge_id']}")
                return True
            else:
                print(f"✗ Missing fields in message: {missing_fields}")
                print(f"  Received message: {message}")
                return False

        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            return False
    
    def run_all_tests(self):
        """Run all test cases"""
        print("Starting MQTT to RabbitMQ Bridge Test Suite")
        print("=" * 50)

        try:
            # Wait for services to be ready
            self.wait_for_services()

            # Setup
            self.setup_mqtt_publisher()
            self.setup_rabbitmq_consumer()

            print("\nNote: Make sure the bridge is running before running tests!")
            print("You can start it with: cd ../bridge && python3 bridge.py")
            print("Or with docker-compose: docker compose up bridge")

            # Run tests
            tests = [
                self.test_basic_message_forwarding,
                self.test_invalid_json_handling,
                self.test_message_structure
            ]

            results = []
            for test in tests:
                try:
                    result = test()
                    results.append(result)
                    # Small delay between tests
                    time.sleep(1)
                except Exception as e:
                    print(f"✗ Test failed with exception: {e}")
                    results.append(False)

            # Summary
            passed = sum(results)
            total = len(results)

            print(f"\n{'='*50}")
            print(f"Test Results: {passed}/{total} tests passed")

            if passed == total:
                print("🎉 All tests passed! Bridge is working correctly.")
                return True
            else:
                print("⚠️  Some tests failed. Check the bridge configuration and connections.")
                return False

        except Exception as e:
            print(f"Test suite setup failed: {e}")
            return False
        finally:
            # Cleanup
            try:
                if self.mqtt_client:
                    self.mqtt_client.loop_stop()
                    self.mqtt_client.disconnect()
            except Exception as e:
                print(f"Error cleaning up MQTT client: {e}")

            try:
                if self.rabbitmq_connection and not self.rabbitmq_connection.is_closed:
                    self.rabbitmq_connection.close()
            except Exception as e:
                print(f"Error cleaning up RabbitMQ connection: {e}")

def main():
    """Main entry point for tests"""
    test_suite = BridgeTestSuite()
    test_suite.run_all_tests()

if __name__ == "__main__":
    main()

"""
Test script for MQTT to RabbitMQ Bridge
Provides utilities to test the bridge functionality
"""
import json
import time
import threading
import sys
import os

# Add bridge directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'bridge'))

import paho.mqtt.client as mqtt
import pika
from datetime import datetime, timezone

# Test configuration
TEST_MQTT_CONFIG = {
    'broker_host': 'localhost',
    'broker_port': 1883,
    'client_id': 'test_publisher',
    'topic_base': 'devices'
}

TEST_RABBITMQ_CONFIG = {
    'host': 'localhost',
    'port': 5672,
    'username': 'guest',
    'password': 'guest',
    'queue_name': 'iot_data'
}

class BridgeTestSuite:
    """Test suite for the MQTT to RabbitMQ bridge"""
    
    def __init__(self):
        self.mqtt_client = None
        self.rabbitmq_connection = None
        self.rabbitmq_channel = None
        self.received_messages = []
        
    def setup_mqtt_publisher(self):
        """Setup MQTT client for publishing test messages"""
        self.mqtt_client = mqtt.Client(client_id=TEST_MQTT_CONFIG['client_id'])
        self.mqtt_client.connect(
            TEST_MQTT_CONFIG['broker_host'],
            TEST_MQTT_CONFIG['broker_port'],
            60
        )
        self.mqtt_client.loop_start()
        print("✓ MQTT test publisher connected")
    
    def setup_rabbitmq_consumer(self):
        """Setup RabbitMQ consumer to verify message delivery"""
        credentials = pika.PlainCredentials(
            TEST_RABBITMQ_CONFIG['username'],
            TEST_RABBITMQ_CONFIG['password']
        )
        
        parameters = pika.ConnectionParameters(
            host=TEST_RABBITMQ_CONFIG['host'],
            port=TEST_RABBITMQ_CONFIG['port'],
            credentials=credentials
        )
        
        self.rabbitmq_connection = pika.BlockingConnection(parameters)
        self.rabbitmq_channel = self.rabbitmq_connection.channel()
        
        # Declare queue (should already exist from bridge)
        self.rabbitmq_channel.queue_declare(
            queue=TEST_RABBITMQ_CONFIG['queue_name'],
            durable=True
        )
        
        print("✓ RabbitMQ test consumer connected")
    
    def consume_rabbitmq_messages(self, timeout=10):
        """Consume messages from RabbitMQ queue"""
        def callback(ch, method, properties, body):
            try:
                message = json.loads(body.decode('utf-8'))
                self.received_messages.append(message)
                print(f"✓ Received from RabbitMQ: {message['topic']} -> {message['payload']}")
                ch.basic_ack(delivery_tag=method.delivery_tag)
            except Exception as e:
                print(f"✗ Error processing RabbitMQ message: {e}")
        
        self.rabbitmq_channel.basic_consume(
            queue=TEST_RABBITMQ_CONFIG['queue_name'],
            on_message_callback=callback
        )
        
        # Start consuming in a separate thread
        def consume_thread():
            try:
                self.rabbitmq_channel.start_consuming()
            except Exception as e:
                print(f"Consumer thread error: {e}")
        
        consumer_thread = threading.Thread(target=consume_thread, daemon=True)
        consumer_thread.start()
        
        # Wait for messages
        time.sleep(timeout)
        self.rabbitmq_channel.stop_consuming()
    
    def publish_test_message(self, device_id, payload, topic_suffix="data"):
        """Publish a test message to MQTT"""
        topic = f"{TEST_MQTT_CONFIG['topic_base']}/{device_id}/{topic_suffix}"
        message = json.dumps(payload)
        
        self.mqtt_client.publish(topic, message, qos=1)
        print(f"✓ Published to MQTT: {topic} -> {payload}")
        time.sleep(0.5)  # Small delay to ensure message is sent
    
    def test_basic_message_forwarding(self):
        """Test basic message forwarding from MQTT to RabbitMQ"""
        print("\n=== Testing Basic Message Forwarding ===")
        
        # Clear received messages
        self.received_messages = []
        
        # Test messages
        test_cases = [
            {
                'device_id': 'tablet001',
                'payload': {
                    'temperature': 23.5,
                    'humidity': 65.2,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
            },
            {
                'device_id': 'tablet002',
                'payload': {
                    'battery_level': 85,
                    'signal_strength': -45,
                    'location': {'lat': 40.7128, 'lon': -74.0060}
                }
            },
            {
                'device_id': 'sensor001',
                'payload': {
                    'motion_detected': True,
                    'light_level': 450
                }
            }
        ]
        
        # Publish test messages
        for test_case in test_cases:
            self.publish_test_message(test_case['device_id'], test_case['payload'])
        
        # Start consuming and wait for messages
        print("Waiting for messages to be forwarded...")
        self.consume_rabbitmq_messages(timeout=5)
        
        # Verify results
        print(f"\nResults: {len(self.received_messages)} messages received")
        
        if len(self.received_messages) >= len(test_cases):
            print("✓ All test messages were successfully forwarded")
            return True
        else:
            print(f"✗ Expected {len(test_cases)} messages, got {len(self.received_messages)}")
            return False
    
    def test_invalid_json_handling(self):
        """Test handling of non-JSON messages"""
        print("\n=== Testing Invalid JSON Handling ===")
        
        self.received_messages = []
        
        # Publish non-JSON message
        topic = f"{TEST_MQTT_CONFIG['topic_base']}/tablet003/raw"
        invalid_message = "This is not JSON data"
        
        self.mqtt_client.publish(topic, invalid_message, qos=1)
        print(f"✓ Published non-JSON message: {invalid_message}")
        
        # Wait for forwarding
        time.sleep(2)
        self.consume_rabbitmq_messages(timeout=3)
        
        # Check if message was still forwarded (as string)
        if self.received_messages:
            forwarded_msg = self.received_messages[0]
            if forwarded_msg['payload'] == invalid_message:
                print("✓ Non-JSON message correctly forwarded as string")
                return True
        
        print("✗ Non-JSON message handling failed")
        return False
    
    def test_message_structure(self):
        """Test the structure of forwarded messages"""
        print("\n=== Testing Message Structure ===")
        
        self.received_messages = []
        
        # Publish a test message
        test_payload = {'test': 'structure_validation'}
        self.publish_test_message('test_device', test_payload)
        
        # Consume message
        self.consume_rabbitmq_messages(timeout=3)
        
        if not self.received_messages:
            print("✗ No message received for structure test")
            return False
        
        message = self.received_messages[0]
        required_fields = ['topic', 'payload', 'timestamp', 'bridge_id']
        
        missing_fields = [field for field in required_fields if field not in message]
        
        if not missing_fields:
            print("✓ Message structure is correct")
            print(f"  - Topic: {message['topic']}")
            print(f"  - Payload: {message['payload']}")
            print(f"  - Timestamp: {message['timestamp']}")
            print(f"  - Bridge ID: {message['bridge_id']}")
            return True
        else:
            print(f"✗ Missing fields in message: {missing_fields}")
            return False
    
    def run_all_tests(self):
        """Run all test cases"""
        print("Starting MQTT to RabbitMQ Bridge Test Suite")
        print("=" * 50)
        
        try:
            # Setup
            self.setup_mqtt_publisher()
            self.setup_rabbitmq_consumer()
            
            # Run tests
            tests = [
                self.test_basic_message_forwarding,
                self.test_invalid_json_handling,
                self.test_message_structure
            ]
            
            results = []
            for test in tests:
                try:
                    result = test()
                    results.append(result)
                except Exception as e:
                    print(f"✗ Test failed with exception: {e}")
                    results.append(False)
            
            # Summary
            passed = sum(results)
            total = len(results)
            
            print(f"\n{'='*50}")
            print(f"Test Results: {passed}/{total} tests passed")
            
            if passed == total:
                print("🎉 All tests passed! Bridge is working correctly.")
            else:
                print("⚠️  Some tests failed. Check the bridge configuration and connections.")
            
        except Exception as e:
            print(f"Test suite setup failed: {e}")
        finally:
            # Cleanup
            if self.mqtt_client:
                self.mqtt_client.loop_stop()
                self.mqtt_client.disconnect()
            
            if self.rabbitmq_connection and not self.rabbitmq_connection.is_closed:
                self.rabbitmq_connection.close()

def main():
    """Main entry point for tests"""
    test_suite = BridgeTestSuite()
    test_suite.run_all_tests()

if __name__ == "__main__":
    main()

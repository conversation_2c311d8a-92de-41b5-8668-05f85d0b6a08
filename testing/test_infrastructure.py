#!/usr/bin/env python3
"""
Simple infrastructure connectivity test
Tests basic connectivity to MQTT and RabbitMQ without the bridge
"""
import sys
import time
import json

try:
    import paho.mqtt.client as mqtt
    import pika
except ImportError as e:
    print(f"Missing dependencies: {e}")
    print("Install with: pip install paho-mqtt pika")
    sys.exit(1)

def test_mqtt_basic():
    """Test basic MQTT connectivity"""
    print("Testing MQTT connectivity...")
    
    try:
        # Create client
        client = mqtt.Client(client_id="infrastructure_test")
        
        # Connect
        client.connect("localhost", 1883, 60)
        client.loop_start()
        
        # Wait for connection
        time.sleep(1)
        
        # Test publish
        result = client.publish("test/infrastructure", "test message")
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print("✓ MQTT broker is accessible and working")
            success = True
        else:
            print(f"✗ MQTT publish failed with code: {result.rc}")
            success = False
        
        client.loop_stop()
        client.disconnect()
        return success
        
    except Exception as e:
        print(f"✗ MQTT test failed: {e}")
        return False

def test_rabbitmq_basic():
    """Test basic RabbitMQ connectivity"""
    print("Testing RabbitMQ connectivity...")
    
    try:
        # Create connection
        credentials = pika.PlainCredentials('guest', 'guest')
        parameters = pika.ConnectionParameters(
            host='localhost',
            port=5672,
            credentials=credentials,
            socket_timeout=10
        )
        
        connection = pika.BlockingConnection(parameters)
        channel = connection.channel()
        
        # Declare a test queue
        queue_result = channel.queue_declare(queue='test_infrastructure', durable=False)
        
        # Publish a test message
        channel.basic_publish(
            exchange='',
            routing_key='test_infrastructure',
            body=json.dumps({'test': 'infrastructure', 'timestamp': time.time()})
        )
        
        print("✓ RabbitMQ broker is accessible and working")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"✗ RabbitMQ test failed: {e}")
        return False

def test_queue_operations():
    """Test RabbitMQ queue operations"""
    print("Testing RabbitMQ queue operations...")
    
    try:
        # Connect
        credentials = pika.PlainCredentials('guest', 'guest')
        parameters = pika.ConnectionParameters(
            host='localhost',
            port=5672,
            credentials=credentials
        )
        
        connection = pika.BlockingConnection(parameters)
        channel = connection.channel()
        
        # Declare the iot_data queue (same as bridge uses)
        channel.queue_declare(queue='iot_data', durable=True)
        
        # Publish a test message
        test_message = {
            'topic': 'test/infrastructure/queue',
            'payload': {'test': 'queue_operations'},
            'timestamp': time.time(),
            'bridge_id': 'infrastructure_test'
        }
        
        channel.basic_publish(
            exchange='',
            routing_key='iot_data',
            body=json.dumps(test_message),
            properties=pika.BasicProperties(delivery_mode=2)  # Persistent
        )
        
        # Try to consume the message
        method_frame, header_frame, body = channel.basic_get(queue='iot_data')
        
        if method_frame:
            received_message = json.loads(body.decode('utf-8'))
            channel.basic_ack(method_frame.delivery_tag)
            print("✓ RabbitMQ queue operations working correctly")
            print(f"  Published and consumed: {received_message['topic']}")
            success = True
        else:
            print("✗ Failed to consume test message from queue")
            success = False
        
        connection.close()
        return success
        
    except Exception as e:
        print(f"✗ RabbitMQ queue test failed: {e}")
        return False

def main():
    """Run infrastructure tests"""
    print("IoT Infrastructure Connectivity Test")
    print("=" * 35)
    
    tests = [
        ("MQTT Basic", test_mqtt_basic),
        ("RabbitMQ Basic", test_rabbitmq_basic),
        ("RabbitMQ Queue Ops", test_queue_operations)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n{'='*35}")
    print(f"Infrastructure Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ Infrastructure is ready for bridge testing!")
        return True
    else:
        print("✗ Infrastructure issues detected. Fix before running bridge tests.")
        print("\nTroubleshooting:")
        print("1. Start services: docker compose up -d mosquitto rabbitmq redis")
        print("2. Check logs: docker compose logs mosquitto rabbitmq")
        print("3. Verify ports: netstat -tlnp | grep -E '1883|5672'")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

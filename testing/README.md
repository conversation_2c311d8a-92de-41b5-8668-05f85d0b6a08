# Testing Suite for IoT Bridge

This directory contains comprehensive tests for the MQTT to RabbitMQ bridge system.

## Test Types

### 1. Client Unit Tests (`test_clients.py`)
Tests individual MQTT and RabbitMQ client classes:
- Connection establishment
- Message publishing
- MQTT subscription
- Error handling

**Requirements**: MQTT broker and RabbitMQ running

### 2. Integration Tests (`test_bridge.py`)
Tests the complete bridge functionality:
- Message forwarding from MQTT to RabbitMQ
- JSON and non-JSON message handling
- Message structure validation

**Requirements**: MQTT broker, RabbitMQ, and bridge application running

### 3. Full Test Suite (`run_tests.py`)
Automated test runner that:
- Starts infrastructure services via Docker Compose
- Starts the bridge application
- Runs integration tests
- Cleans up afterwards

**Requirements**: <PERSON><PERSON> and <PERSON>er Compose

### 4. Comprehensive Test Runner (`test_all.py`)
Master test runner with multiple options:
- Run specific test types
- Check dependencies
- Show test information

## Quick Start

### Option 1: Full Automated Testing (Recommended)
```bash
# Install dependencies
pip install -r requirements.txt

# Run full automated test suite
python3 run_tests.py
```

### Option 2: Manual Testing
```bash
# Start infrastructure (from project root)
cd ..
docker compose up -d mosquitto rabbitmq redis

# Start bridge (in separate terminal)
cd bridge
python3 bridge.py

# Run tests (in another terminal)
cd testing
python3 test_bridge.py
```

### Option 3: Client Tests Only
```bash
# Start infrastructure
cd ..
docker compose up -d mosquitto rabbitmq redis

# Run client tests
cd testing
python3 test_clients.py
```

## Test Runner Options

### Using test_all.py
```bash
# Check dependencies and files
python3 test_all.py --check

# Show test information
python3 test_all.py --info

# Run specific test types
python3 test_all.py --type client      # Client unit tests only
python3 test_all.py --type integration # Integration tests only
python3 test_all.py --type full        # Full automated suite
python3 test_all.py --type all         # Client + integration (default)
```

## Test Files

- **`test_clients.py`** - Unit tests for MQTT and RabbitMQ clients
- **`test_bridge.py`** - Integration tests for bridge functionality
- **`run_tests.py`** - Automated test runner with infrastructure management
- **`test_all.py`** - Comprehensive test runner with multiple options
- **`example_usage.py`** - Examples of using clients independently
- **`publish_test_data.py`** - IoT data simulator for testing

## Expected Test Results

### Successful Test Run
```
Starting MQTT to RabbitMQ Bridge Test Suite
==================================================
✓ MQTT test publisher connected
✓ RabbitMQ test consumer connected

=== Testing Basic Message Forwarding ===
✓ Published to MQTT: devices/tablet001/data -> {...}
Waiting for messages to be forwarded...
✓ Received from RabbitMQ: devices/tablet001/data -> {...}

Results: 1 messages received
✓ All test messages were successfully forwarded

=== Testing Invalid JSON Handling ===
✓ Published non-JSON message: This is not JSON data
✓ Non-JSON message correctly forwarded as string

=== Testing Message Structure ===
✓ Published to MQTT: devices/test_device/data -> {...}
✓ Message structure is correct
  - Topic: devices/test_device/data
  - Payload: {'test': 'structure_validation'}
  - Timestamp: 2024-01-01T12:00:01.123456Z
  - Bridge ID: iot_bridge_client

==================================================
Test Results: 3/3 tests passed
🎉 All tests passed! Bridge is working correctly.
```

## Troubleshooting

### Common Issues

1. **"Connection refused" errors**
   - Ensure MQTT broker and RabbitMQ are running
   - Check if services are accessible on localhost:1883 and localhost:5672

2. **"Bridge not running" test failures**
   - Start the bridge application: `cd ../bridge && python3 bridge.py`
   - Or use the full automated test suite: `python3 run_tests.py`

3. **"Channel is closed" errors**
   - RabbitMQ connection issues
   - Restart RabbitMQ: `docker compose restart rabbitmq`

4. **Import errors**
   - Install dependencies: `pip install -r requirements.txt`
   - Ensure bridge files are present in `../bridge/`

### Debug Commands

```bash
# Check if services are running
docker compose ps

# View service logs
docker compose logs mosquitto
docker compose logs rabbitmq

# Test MQTT connection manually
mosquitto_pub -h localhost -t "test/topic" -m "test message"

# Test RabbitMQ connection
docker compose exec rabbitmq rabbitmqctl status
```

## Dependencies

The testing suite requires:
- `paho-mqtt` - MQTT client library
- `pika` - RabbitMQ client library
- `tenacity` - Retry logic
- `python-dotenv` - Environment variables
- `pytest` - Testing framework (optional)

Install with:
```bash
pip install -r requirements.txt
```

## Test Configuration

Tests use the following default configuration:
- **MQTT Broker**: localhost:1883
- **RabbitMQ**: localhost:5672 (guest/guest)
- **Queue Name**: iot_data
- **Topic Pattern**: devices/#

Configuration can be modified in the test files if needed.

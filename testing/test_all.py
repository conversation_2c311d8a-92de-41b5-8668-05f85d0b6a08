#!/usr/bin/env python3
"""
Comprehensive test runner for the IoT Bridge system
"""
import sys
import os
import argparse
import subprocess
import time

def run_client_tests():
    """Run client unit tests"""
    print("Running Client Unit Tests")
    print("=" * 30)
    
    try:
        result = subprocess.run([sys.executable, 'test_clients.py'], timeout=60)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("✗ Client tests timed out")
        return False
    except Exception as e:
        print(f"✗ Error running client tests: {e}")
        return False

def run_integration_tests():
    """Run integration tests"""
    print("Running Integration Tests")
    print("=" * 30)
    
    try:
        result = subprocess.run([sys.executable, 'test_bridge.py'], timeout=120)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("✗ Integration tests timed out")
        return False
    except Exception as e:
        print(f"✗ Error running integration tests: {e}")
        return False

def run_full_test_suite():
    """Run the full automated test suite"""
    print("Running Full Test Suite with Infrastructure")
    print("=" * 45)
    
    try:
        result = subprocess.run([sys.executable, 'run_tests.py'], timeout=300)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("✗ Full test suite timed out")
        return False
    except Exception as e:
        print(f"✗ Error running full test suite: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    print("Checking Dependencies")
    print("=" * 20)
    
    required_modules = ['paho.mqtt.client', 'pika', 'tenacity']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError:
            print(f"✗ {module} (missing)")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\nMissing dependencies: {missing_modules}")
        print("Install with: pip install -r requirements.txt")
        return False
    
    print("✓ All dependencies available")
    return True

def check_bridge_files():
    """Check if bridge files are available"""
    print("\nChecking Bridge Files")
    print("=" * 20)
    
    bridge_dir = os.path.join('..', 'bridge')
    required_files = ['bridge.py', 'config.py', 'mqtt_client.py', 'rabbitmq_client.py']
    missing_files = []
    
    for file in required_files:
        file_path = os.path.join(bridge_dir, file)
        if os.path.exists(file_path):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} (missing)")
            missing_files.append(file)
    
    if missing_files:
        print(f"\nMissing bridge files: {missing_files}")
        return False
    
    print("✓ All bridge files available")
    return True

def show_test_info():
    """Show information about available tests"""
    print("""
Available Test Types:

1. Client Tests (test_clients.py)
   - Unit tests for individual MQTT and RabbitMQ clients
   - Tests connection, publishing, and subscription
   - Requires: MQTT broker and RabbitMQ running

2. Integration Tests (test_bridge.py)
   - Tests the complete bridge functionality
   - Tests message forwarding from MQTT to RabbitMQ
   - Requires: MQTT broker, RabbitMQ, and bridge application running

3. Full Test Suite (run_tests.py)
   - Automatically starts infrastructure and bridge
   - Runs complete integration tests
   - Requires: Docker and Docker Compose

Prerequisites:
- For client/integration tests: Start services manually
  docker compose up -d mosquitto rabbitmq redis
  
- For integration tests: Also start the bridge
  cd ../bridge && python3 bridge.py
  
- For full suite: Just have Docker Compose available
""")

def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description='IoT Bridge Test Runner')
    parser.add_argument('--type', choices=['client', 'integration', 'full', 'all'], 
                       default='all', help='Type of tests to run')
    parser.add_argument('--check', action='store_true', 
                       help='Check dependencies and files only')
    parser.add_argument('--info', action='store_true', 
                       help='Show test information')
    
    args = parser.parse_args()
    
    if args.info:
        show_test_info()
        return
    
    print("IoT Bridge Test Runner")
    print("=" * 25)
    
    # Check dependencies and files
    if not check_dependencies():
        sys.exit(1)
    
    if not check_bridge_files():
        sys.exit(1)
    
    if args.check:
        print("\n✓ All checks passed!")
        return
    
    # Run tests based on type
    results = []
    
    if args.type in ['client', 'all']:
        print(f"\n{'='*50}")
        success = run_client_tests()
        results.append(('Client Tests', success))
    
    if args.type in ['integration', 'all']:
        print(f"\n{'='*50}")
        success = run_integration_tests()
        results.append(('Integration Tests', success))
    
    if args.type == 'full':
        print(f"\n{'='*50}")
        success = run_full_test_suite()
        results.append(('Full Test Suite', success))
    
    # Summary
    if results:
        print(f"\n{'='*50}")
        print("Test Summary:")
        
        all_passed = True
        for test_name, success in results:
            status = "PASSED" if success else "FAILED"
            icon = "✓" if success else "✗"
            print(f"  {icon} {test_name}: {status}")
            if not success:
                all_passed = False
        
        if all_passed:
            print("\n🎉 All tests passed!")
            sys.exit(0)
        else:
            print("\n⚠️  Some tests failed. Check the output above.")
            sys.exit(1)

if __name__ == "__main__":
    main()

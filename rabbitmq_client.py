"""
RabbitMQ Client for IoT Bridge
Handles RabbitMQ connection, queue management, and message publishing
"""
import json
import time
import logging
from typing import Optional, Dict, Any, Callable

import pika
from pika.exceptions import AMQPConnectionError, AMQPChannelError
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from config import RABBITMQ_CONFIG, BRIDGE_CONFIG


class RabbitMQClient:
    """
    RabbitMQ client wrapper with auto-reconnection and message publishing
    """
    
    def __init__(self):
        """Initialize RabbitMQ client"""
        self.logger = logging.getLogger(__name__)
        self.connection = None
        self.channel = None
        self.connected = False
        self.message_count = 0
        
        self.logger.info("RabbitMQ Client initialized")
    
    @retry(
        stop=stop_after_attempt(BRIDGE_CONFIG['max_retries']),
        wait=wait_exponential(multiplier=1, min=1, max=60),
        retry=retry_if_exception_type((AMQPConnectionError, AMQPChannelError))
    )
    def connect(self) -> bool:
        """
        Connect to RabbitMQ broker
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Create connection parameters
            credentials = pika.PlainCredentials(
                RABBITMQ_CONFIG['username'],
                RABBITMQ_CONFIG['password']
            )
            
            parameters = pika.ConnectionParameters(
                host=RABBITMQ_CONFIG['host'],
                port=RABBITMQ_CONFIG['port'],
                virtual_host=RABBITMQ_CONFIG['virtual_host'],
                credentials=credentials,
                connection_attempts=3,
                retry_delay=2,
                socket_timeout=RABBITMQ_CONFIG['connection_timeout'],
                heartbeat=RABBITMQ_CONFIG['heartbeat']
            )
            
            # Establish connection
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
            
            # Declare queue as durable
            self.channel.queue_declare(
                queue=RABBITMQ_CONFIG['queue_name'],
                durable=RABBITMQ_CONFIG['durable']
            )
            
            self.connected = True
            self.logger.info(f"Connected to RabbitMQ at {RABBITMQ_CONFIG['host']}:{RABBITMQ_CONFIG['port']}")
            self.logger.info(f"Queue '{RABBITMQ_CONFIG['queue_name']}' declared as durable")
            
            return True
            
        except Exception as e:
            self.connected = False
            self.logger.error(f"Failed to connect to RabbitMQ: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from RabbitMQ broker"""
        self.connected = False
        
        # Close channel
        if self.channel and not self.channel.is_closed:
            try:
                self.channel.close()
                self.logger.info("RabbitMQ channel closed")
            except Exception as e:
                self.logger.warning(f"Error closing RabbitMQ channel: {e}")
        
        # Close connection
        if self.connection and not self.connection.is_closed:
            try:
                self.connection.close()
                self.logger.info("RabbitMQ connection closed")
            except Exception as e:
                self.logger.warning(f"Error closing RabbitMQ connection: {e}")
    
    @retry(
        stop=stop_after_attempt(BRIDGE_CONFIG['max_retries']),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((AMQPConnectionError, AMQPChannelError))
    )
    def publish(self, message: Dict[str, Any], 
                queue_name: Optional[str] = None,
                exchange_name: Optional[str] = None,
                routing_key: Optional[str] = None,
                persistent: Optional[bool] = None) -> bool:
        """
        Publish message to RabbitMQ queue
        
        Args:
            message: Message to publish (will be JSON encoded)
            queue_name: Queue name (defaults to config)
            exchange_name: Exchange name (defaults to config)
            routing_key: Routing key (defaults to config)
            persistent: Message persistence (defaults to config)
            
        Returns:
            bool: True if publish successful, False otherwise
        """
        try:
            # Ensure connection is alive
            if not self.is_connected():
                self.logger.warning("RabbitMQ connection lost, reconnecting...")
                if not self.connect():
                    return False
            
            # Use config defaults if not specified
            queue = queue_name or RABBITMQ_CONFIG['queue_name']
            exchange = exchange_name or RABBITMQ_CONFIG['exchange_name']
            routing = routing_key or RABBITMQ_CONFIG['routing_key']
            persist = persistent if persistent is not None else RABBITMQ_CONFIG['persistent']
            
            # Convert message to JSON string
            message_body = json.dumps(message)
            
            # Publish message with persistence
            self.channel.basic_publish(
                exchange=exchange,
                routing_key=routing,
                body=message_body,
                properties=pika.BasicProperties(
                    delivery_mode=2 if persist else 1,  # 2 = persistent
                    content_type='application/json',
                    timestamp=int(time.time())
                )
            )
            
            self.message_count += 1
            self.logger.debug(f"Published message to queue '{queue}', total: {self.message_count}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to publish message to RabbitMQ: {e}")
            return False
    
    def consume(self, callback: Callable, 
                queue_name: Optional[str] = None,
                auto_ack: bool = False) -> bool:
        """
        Start consuming messages from queue
        
        Args:
            callback: Function to call for each message
            queue_name: Queue name (defaults to config)
            auto_ack: Auto-acknowledge messages
            
        Returns:
            bool: True if consumption started successfully, False otherwise
        """
        try:
            if not self.is_connected():
                self.logger.error("Cannot start consuming: not connected to RabbitMQ")
                return False
            
            queue = queue_name or RABBITMQ_CONFIG['queue_name']
            
            self.channel.basic_consume(
                queue=queue,
                on_message_callback=callback,
                auto_ack=auto_ack
            )
            
            self.logger.info(f"Started consuming from queue '{queue}'")
            self.channel.start_consuming()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start consuming: {e}")
            return False
    
    def stop_consuming(self):
        """Stop consuming messages"""
        if self.channel and not self.channel.is_closed:
            try:
                self.channel.stop_consuming()
                self.logger.info("Stopped consuming messages")
            except Exception as e:
                self.logger.warning(f"Error stopping consumption: {e}")
    
    def declare_queue(self, queue_name: str, durable: bool = True, 
                     exclusive: bool = False, auto_delete: bool = False) -> bool:
        """
        Declare a queue
        
        Args:
            queue_name: Name of the queue
            durable: Queue survives broker restart
            exclusive: Queue is exclusive to this connection
            auto_delete: Queue is deleted when last consumer disconnects
            
        Returns:
            bool: True if queue declared successfully, False otherwise
        """
        try:
            if not self.is_connected():
                self.logger.error("Cannot declare queue: not connected to RabbitMQ")
                return False
            
            self.channel.queue_declare(
                queue=queue_name,
                durable=durable,
                exclusive=exclusive,
                auto_delete=auto_delete
            )
            
            self.logger.info(f"Queue '{queue_name}' declared successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to declare queue '{queue_name}': {e}")
            return False
    
    def purge_queue(self, queue_name: Optional[str] = None) -> bool:
        """
        Purge all messages from queue
        
        Args:
            queue_name: Queue name (defaults to config)
            
        Returns:
            bool: True if purge successful, False otherwise
        """
        try:
            if not self.is_connected():
                self.logger.error("Cannot purge queue: not connected to RabbitMQ")
                return False
            
            queue = queue_name or RABBITMQ_CONFIG['queue_name']
            method = self.channel.queue_purge(queue=queue)
            
            self.logger.info(f"Purged {method.method.message_count} messages from queue '{queue}'")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to purge queue: {e}")
            return False
    
    def get_queue_info(self, queue_name: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get queue information
        
        Args:
            queue_name: Queue name (defaults to config)
            
        Returns:
            Dict with queue info or None if failed
        """
        try:
            if not self.is_connected():
                self.logger.error("Cannot get queue info: not connected to RabbitMQ")
                return None
            
            queue = queue_name or RABBITMQ_CONFIG['queue_name']
            method = self.channel.queue_declare(queue=queue, passive=True)
            
            return {
                'queue': queue,
                'message_count': method.method.message_count,
                'consumer_count': method.method.consumer_count
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get queue info: {e}")
            return None
    
    def is_connected(self) -> bool:
        """Check if client is connected"""
        return (self.connected and 
                self.connection and not self.connection.is_closed and
                self.channel and not self.channel.is_closed)
    
    def get_message_count(self) -> int:
        """Get total number of messages published"""
        return self.message_count

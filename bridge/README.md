# MQTT to RabbitMQ Bridge Application

This directory contains the MQTT to RabbitMQ bridge application that forwards IoT device messages from MQTT topics to RabbitMQ queues.

## Architecture

The bridge application consists of three main components:

### Core Components

- **`bridge.py`** - Main application orchestrating the message flow
- **`mqtt_client.py`** - MQTT client handling connections and subscriptions
- **`rabbitmq_client.py`** - RabbitMQ client handling queue operations
- **`config.py`** - Configuration management with environment variable support

### Message Flow

1. **MQTT Subscription**: Connects to MQTT broker and subscribes to `devices/#` topics
2. **Message Processing**: Receives messages and wraps them in JSON format with metadata
3. **RabbitMQ Publishing**: Forwards processed messages to RabbitMQ queue with persistence

## Configuration

The application is configured via environment variables:

### MQTT Settings
- `MQTT_BROKER_HOST` - MQTT broker hostname (default: localhost)
- `MQTT_BROKER_PORT` - MQTT broker port (default: 1883)
- `MQTT_CLIENT_ID` - MQTT client identifier (default: iot_bridge_client)
- `MQTT_TOPIC_PATTERN` - Topic subscription pattern (default: devices/#)
- `MQTT_USERNAME` - MQTT authentication username (optional)
- `MQTT_PASSWORD` - MQTT authentication password (optional)

### RabbitMQ Settings
- `RABBITMQ_HOST` - RabbitMQ hostname (default: localhost)
- `RABBITMQ_PORT` - RabbitMQ port (default: 5672)
- `RABBITMQ_USERNAME` - RabbitMQ username (default: guest)
- `RABBITMQ_PASSWORD` - RabbitMQ password (default: guest)
- `RABBITMQ_QUEUE_NAME` - Target queue name (default: iot_data)

### Logging Settings
- `LOG_LEVEL` - Logging level (default: INFO)
- `LOG_FORMAT` - Log message format
- `LOG_FILE` - Log file path (optional, logs to console if not set)

## Running the Application

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Run the bridge
python bridge.py
```

### Docker Container

```bash
# Build the image
docker build -t iot-bridge .

# Run the container
docker run -d \
  --name iot-bridge \
  -e MQTT_BROKER_HOST=your-mqtt-host \
  -e RABBITMQ_HOST=your-rabbitmq-host \
  iot-bridge
```

### Docker Compose (Recommended)

The bridge is designed to run as part of the complete IoT infrastructure using Docker Compose from the project root:

```bash
# From project root
docker-compose up -d
```

## Message Format

The bridge transforms MQTT messages into the following JSON structure:

```json
{
  "topic": "devices/tablet001/data",
  "payload": {
    "temperature": 23.5,
    "humidity": 65.2,
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "timestamp": "2024-01-01T12:00:01.123456Z",
  "bridge_id": "iot_bridge_client"
}
```

## Health Monitoring

The application includes:

- **Connection Health Checks**: Monitors MQTT and RabbitMQ connections
- **Message Statistics**: Tracks processed messages and errors
- **Auto-Reconnection**: Automatically reconnects on connection failures
- **Graceful Shutdown**: Handles SIGINT/SIGTERM signals properly

## Error Handling

- **Retry Logic**: Exponential backoff for connection failures
- **Message Persistence**: RabbitMQ messages are durable and persistent
- **Non-JSON Messages**: Handles non-JSON MQTT payloads gracefully
- **Comprehensive Logging**: Detailed error logging and debugging information

## Development

### Testing Individual Components

```bash
# Test MQTT client
python -c "from mqtt_client import MQTTClient; client = MQTTClient(); client.connect()"

# Test RabbitMQ client  
python -c "from rabbitmq_client import RabbitMQClient; client = RabbitMQClient(); client.connect()"
```

### Environment Variables for Development

Create a `.env` file in this directory:

```bash
MQTT_BROKER_HOST=localhost
RABBITMQ_HOST=localhost
LOG_LEVEL=DEBUG
```

## Security Considerations

- Runs as non-root user in Docker container
- Supports MQTT authentication
- RabbitMQ credentials configurable
- No sensitive data in logs (passwords masked)

## Performance

- Asynchronous MQTT message handling
- Connection pooling for RabbitMQ
- Configurable retry policies
- Health check monitoring
